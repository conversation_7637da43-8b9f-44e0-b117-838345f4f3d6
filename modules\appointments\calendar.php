<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

// Lấy tháng/năm hiện tại hoặc từ URL
$month = isset($_GET['month']) ? (int)$_GET['month'] : date('n');
$year = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');

// Validate month/year
if ($month < 1 || $month > 12) $month = date('n');
if ($year < 2020 || $year > 2030) $year = date('Y');

// Tính toán ngày đầu và cuối tháng
$first_day = date('Y-m-01', mktime(0, 0, 0, $month, 1, $year));
$last_day = date('Y-m-t', mktime(0, 0, 0, $month, 1, $year));

// <PERSON><PERSON><PERSON> lịch hẹn trong tháng từ database mới
$sql = "SELECT a.*,
               b.hoten as patient_name,
               b.sdt as patient_phone,
               b.email as patient_email,
               a.service_type,
               a.reason
        FROM appointments a
        LEFT JOIN benhnhan b ON a.patient_id = b.id
        WHERE a.appointment_date BETWEEN ? AND ?
        ORDER BY a.appointment_date, a.appointment_time";

$params = [$first_day, $last_day];
$appointments = $database->fetchAll($sql, $params);

// Đảm bảo $appointments là array
if (!is_array($appointments)) {
    $appointments = [];
}

// Nhóm lịch hẹn theo ngày
$appointments_by_date = [];
foreach ($appointments as $appointment) {
    $date = $appointment['appointment_date'];
    if (!isset($appointments_by_date[$date])) {
        $appointments_by_date[$date] = [];
    }
    $appointments_by_date[$date][] = $appointment;
}

// Tính toán calendar
$days_in_month = date('t', mktime(0, 0, 0, $month, 1, $year));
$first_day_of_week = date('w', mktime(0, 0, 0, $month, 1, $year)); // 0 = Sunday
$first_day_of_week = ($first_day_of_week == 0) ? 7 : $first_day_of_week; // Convert to Monday = 1

// Tháng trước/sau
$prev_month = $month - 1;
$prev_year = $year;
if ($prev_month < 1) {
    $prev_month = 12;
    $prev_year--;
}

$next_month = $month + 1;
$next_year = $year;
if ($next_month > 12) {
    $next_month = 1;
    $next_year++;
}

$month_names = [
    1 => 'Tháng 1', 2 => 'Tháng 2', 3 => 'Tháng 3', 4 => 'Tháng 4',
    5 => 'Tháng 5', 6 => 'Tháng 6', 7 => 'Tháng 7', 8 => 'Tháng 8',
    9 => 'Tháng 9', 10 => 'Tháng 10', 11 => 'Tháng 11', 12 => 'Tháng 12'
];

function getStatusColor($status) {
    $colors = [
        'dat_lich' => '#ffc107',      // Vàng - Đã đặt lịch
        'xac_nhan' => '#17a2b8',      // Xanh dương - Xác nhận
        'dang_kham' => '#007bff',     // Xanh - Đang khám
        'hoan_thanh' => '#28a745',    // Xanh lá - Hoàn thành
        'huy_lich' => '#dc3545',      // Đỏ - Hủy lịch
        'khong_den' => '#6c757d'      // Xám - Không đến
    ];
    return $colors[$status] ?? '#6c757d';
}

function getServiceTypeLabel($service_type) {
    $labels = [
        'kham_tong_quat' => '🔍 Khám tổng quát',
        'tay_trang' => '✨ Tẩy trắng',
        'nho_rang' => '🦷 Nhổ răng',
        'han_tram' => '🔧 Hàn trám',
        'dieu_tri_tuy' => '🩺 Điều trị tủy',
        'nieng_rang' => '📐 Niềng răng',
        'cay_ghep' => '🔩 Cấy ghép',
        'phau_thuat' => '⚕️ Phẫu thuật',
        'tu_van' => '💬 Tư vấn'
    ];
    return $labels[$service_type] ?? '🔍 Khám tổng quát';
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lịch hẹn - <?php echo $month_names[$month] . ' ' . $year; ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .calendar-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin: 20px auto;
            max-width: 1400px;
        }
        
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .calendar-nav {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .nav-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 8px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .month-year {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .calendar-table th {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 5px;
            text-align: center;
            font-weight: 600;
        }
        
        .calendar-table td {
            border: 1px solid #e1e1e1;
            vertical-align: top;
            height: 120px;
            width: 14.28%;
            position: relative;
            padding: 5px;
        }
        
        .calendar-table td:hover {
            background: #f8f9fa;
        }
        
        .day-number {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
            text-align: center;
        }
        
        .today .day-number {
            background: #4CAF50;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            line-height: 25px;
            margin: 0 auto 5px;
        }
        
        .other-month {
            background: #f8f9fa;
            color: #999;
        }
        
        .appointment-item {
            background: #e3f2fd;
            border-left: 3px solid #2196F3;
            padding: 2px 4px;
            margin: 1px 0;
            font-size: 10px;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .appointment-item:hover {
            transform: scale(1.05);
            z-index: 10;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .appointment-time {
            font-weight: 600;
            color: #333;
        }
        
        .appointment-patient {
            color: #666;
        }
        
        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 3px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        .stats-bar {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 10px 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 100px;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: bold;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .appointment-tooltip {
            position: absolute;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            display: none;
            max-width: 200px;
        }
        
        @media (max-width: 768px) {
            .calendar-table {
                font-size: 10px;
            }
            
            .calendar-table td {
                height: 80px;
                padding: 2px;
            }
            
            .appointment-item {
                font-size: 8px;
                padding: 1px 2px;
            }
            
            .calendar-header {
                flex-direction: column;
                gap: 15px;
            }
            
            .stats-bar {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="list.php" class="btn btn-secondary back-btn">← Về danh sách</a>
    
    <div class="calendar-container fade-in">
        <div class="calendar-header">
            <div>
                <h1>
                    <span style="font-size: 40px;">📅</span>
                    Lịch hẹn Nha khoa
                </h1>
            </div>
            
            <div class="calendar-nav">
                <a href="?month=<?php echo $prev_month; ?>&year=<?php echo $prev_year; ?>" class="nav-btn">‹ Tháng trước</a>
                <div class="month-year"><?php echo $month_names[$month] . ' ' . $year; ?></div>
                <a href="?month=<?php echo $next_month; ?>&year=<?php echo $next_year; ?>" class="nav-btn">Tháng sau ›</a>
            </div>
            
            <div>
                <a href="add.php" class="btn btn-primary">+ Đặt lịch hẹn</a>
                <a href="list.php" class="btn btn-secondary">📋 Danh sách</a>
            </div>
        </div>
        
        <!-- Thống kê tháng -->
        <div class="stats-bar">
            <?php
            $month_stats = [
                'total' => count($appointments),
                'dat_lich' => 0,
                'xac_nhan' => 0,
                'hoan_thanh' => 0,
                'huy_lich' => 0
            ];

            foreach ($appointments as $appointment) {
                if (isset($month_stats[$appointment['status']])) {
                    $month_stats[$appointment['status']]++;
                }
            }
            ?>
            <div class="stat-item">
                <div class="stat-number"><?php echo $month_stats['total']; ?></div>
                <div class="stat-label">Tổng lịch hẹn</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo $month_stats['dat_lich']; ?></div>
                <div class="stat-label">Đã đặt</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo $month_stats['xac_nhan']; ?></div>
                <div class="stat-label">Xác nhận</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?php echo $month_stats['hoan_thanh']; ?></div>
                <div class="stat-label">Hoàn thành</div>
            </div>
        </div>
        
        <!-- Calendar -->
        <table class="calendar-table">
            <thead>
                <tr>
                    <th>Thứ 2</th>
                    <th>Thứ 3</th>
                    <th>Thứ 4</th>
                    <th>Thứ 5</th>
                    <th>Thứ 6</th>
                    <th>Thứ 7</th>
                    <th>Chủ nhật</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $current_date = 1;
                $today = date('Y-m-d');
                
                // Tính số tuần cần hiển thị
                $weeks_needed = ceil(($days_in_month + $first_day_of_week - 1) / 7);
                
                for ($week = 0; $week < $weeks_needed; $week++):
                ?>
                    <tr>
                        <?php for ($day_of_week = 1; $day_of_week <= 7; $day_of_week++): ?>
                            <?php
                            $cell_date = $current_date - $first_day_of_week + $day_of_week;
                            $is_current_month = ($cell_date >= 1 && $cell_date <= $days_in_month);
                            
                            if ($is_current_month) {
                                $full_date = sprintf('%04d-%02d-%02d', $year, $month, $cell_date);
                                $is_today = ($full_date == $today);
                                $day_appointments = $appointments_by_date[$full_date] ?? [];
                            } else {
                                $is_today = false;
                                $day_appointments = [];
                            }
                            ?>
                            
                            <td class="<?php echo $is_current_month ? ($is_today ? 'today' : '') : 'other-month'; ?>">
                                <?php if ($is_current_month): ?>
                                    <div class="day-number"><?php echo $cell_date; ?></div>
                                    
                                    <?php foreach ($day_appointments as $appointment): ?>
                                        <div class="appointment-item" 
                                             style="border-left-color: <?php echo getStatusColor($appointment['status']); ?>;"
                                             onclick="showAppointmentDetails(<?php echo $appointment['id']; ?>)"
                                             data-appointment='<?php echo json_encode($appointment); ?>'>
                                            <div class="appointment-time">
                                                <?php echo date('H:i', strtotime($appointment['appointment_time'])); ?>
                                            </div>
                                            <div class="appointment-patient">
                                                <?php echo htmlspecialchars(substr($appointment['patient_name'], 0, 15)); ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    
                                    <?php if (count($day_appointments) > 3): ?>
                                        <div style="font-size: 10px; color: #666; text-align: center;">
                                            +<?php echo count($day_appointments) - 3; ?> khác
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <div class="day-number"><?php echo $cell_date <= 0 ? date('j', mktime(0, 0, 0, $month, $cell_date, $year)) : date('j', mktime(0, 0, 0, $month + 1, $cell_date - $days_in_month, $year)); ?></div>
                                <?php endif; ?>
                            </td>
                        <?php endfor; ?>
                    </tr>
                    <?php $current_date += 7; ?>
                <?php endfor; ?>
            </tbody>
        </table>
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: #ffc107;"></div>
                <span>Đã đặt</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #17a2b8;"></div>
                <span>Xác nhận</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #007bff;"></div>
                <span>Đang khám</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #28a745;"></div>
                <span>Hoàn thành</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #dc3545;"></div>
                <span>Đã hủy</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #6c757d;"></div>
                <span>Không đến</span>
            </div>
        </div>
    </div>
    
    <!-- Tooltip -->
    <div id="appointmentTooltip" class="appointment-tooltip"></div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.calendar-container').classList.add('fade-in');
            
            // Tooltip functionality
            const tooltip = document.getElementById('appointmentTooltip');
            
            document.addEventListener('mouseover', function(e) {
                if (e.target.classList.contains('appointment-item') || e.target.closest('.appointment-item')) {
                    const item = e.target.classList.contains('appointment-item') ? e.target : e.target.closest('.appointment-item');
                    const appointment = JSON.parse(item.dataset.appointment);
                    
                    // Tạo service label từ service_type
                    const serviceLabels = {
                        'kham_tong_quat': '🔍 Khám tổng quát',
                        'tay_trang': '✨ Tẩy trắng',
                        'nho_rang': '🦷 Nhổ răng',
                        'han_tram': '🔧 Hàn trám',
                        'dieu_tri_tuy': '🩺 Điều trị tủy',
                        'nieng_rang': '📐 Niềng răng',
                        'cay_ghep': '🔩 Cấy ghép',
                        'phau_thuat': '⚕️ Phẫu thuật',
                        'tu_van': '💬 Tư vấn'
                    };

                    const serviceLabel = serviceLabels[appointment.service_type] || '🔍 Khám tổng quát';

                    tooltip.innerHTML = `
                        <strong>${appointment.patient_name}</strong><br>
                        📞 ${appointment.patient_phone || 'Chưa có SĐT'}<br>
                        🕐 ${appointment.appointment_time}<br>
                        ${serviceLabel}<br>
                        📋 ${appointment.reason || 'Không có ghi chú'}
                    `;
                    
                    tooltip.style.display = 'block';
                    tooltip.style.left = e.pageX + 10 + 'px';
                    tooltip.style.top = e.pageY + 10 + 'px';
                }
            });
            
            document.addEventListener('mouseout', function(e) {
                if (!e.target.classList.contains('appointment-item') && !e.target.closest('.appointment-item')) {
                    tooltip.style.display = 'none';
                }
            });
        });
        
        function showAppointmentDetails(appointmentId) {
            window.location.href = 'view.php?id=' + appointmentId;
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                document.querySelector('.calendar-nav a:first-child').click();
            } else if (e.key === 'ArrowRight') {
                document.querySelector('.calendar-nav a:last-child').click();
            }
        });
    </script>
</body>
</html>
