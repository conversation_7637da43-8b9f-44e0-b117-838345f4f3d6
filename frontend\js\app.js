/**
 * Main Application Controller
 * Handles page routing and application state
 */

class App {
    constructor() {
        this.currentPage = null;
        this.pageModules = {
            dashboard: null,
            patients: null,
            appointments: null,
            reports: null
        };
    }

    /**
     * Initialize application
     */
    async init() {
        // Initialize authentication
        await auth.init();
    }

    /**
     * Load page content
     */
    async loadPage(pageName) {
        try {
            this.currentPage = pageName;
            
            // Show loading
            this.showPageLoading(true);
            
            // Load page content based on page name
            switch (pageName) {
                case 'dashboard':
                    await this.loadDashboard();
                    break;
                case 'patients':
                    await this.loadPatients();
                    break;
                case 'appointments':
                    await this.loadAppointments();
                    break;
                case 'reports':
                    await this.loadReports();
                    break;
                default:
                    await this.loadDashboard();
            }
            
        } catch (error) {
            console.error('Error loading page:', error);
            api.handleError(error);
        } finally {
            this.showPageLoading(false);
        }
    }

    /**
     * Load Dashboard page
     */
    async loadDashboard() {
        const content = `
            <div class="fade-in">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
                    <button class="btn btn-primary" onclick="app.refreshDashboard()">
                        <i class="fas fa-sync-alt me-1"></i>Làm mới
                    </button>
                </div>
                
                <!-- Stats Cards -->
                <div class="row mb-4" id="statsCards">
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number" id="totalPatients">-</div>
                                    <div class="stats-label">Tổng bệnh nhân</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number" id="todayAppointments">-</div>
                                    <div class="stats-label">Lịch hẹn hôm nay</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number" id="completedToday">-</div>
                                    <div class="stats-label">Đã hoàn thành</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number" id="revenue">-</div>
                                    <div class="stats-label">Doanh thu tháng</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-plus me-2"></i>Thao tác nhanh
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-primary" onclick="patients.showAddModal()">
                                        <i class="fas fa-user-plus me-2"></i>Thêm bệnh nhân mới
                                    </button>
                                    <button class="btn btn-outline-success" onclick="appointments.showAddModal()">
                                        <i class="fas fa-calendar-plus me-2"></i>Đặt lịch hẹn mới
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-clock me-2"></i>Lịch hẹn sắp tới
                            </div>
                            <div class="card-body">
                                <div id="upcomingAppointments">
                                    <div class="text-center">
                                        <div class="spinner-border spinner-border-sm" role="status"></div>
                                        <span class="ms-2">Đang tải...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('pageContent').innerHTML = content;
        
        // Load dashboard data
        await this.loadDashboardData();
    }

    /**
     * Load dashboard data
     */
    async loadDashboardData() {
        try {
            const stats = await api.getStats();
            
            if (stats.success) {
                const data = stats.data;
                
                // Update stats cards
                document.getElementById('totalPatients').textContent = data.total_patients || 0;
                document.getElementById('todayAppointments').textContent = data.today_appointments || 0;
                document.getElementById('completedToday').textContent = data.completed_today || 0;
                document.getElementById('revenue').textContent = api.formatCurrency(data.revenue || 0);
                
                // Update upcoming appointments
                this.renderUpcomingAppointments(data.upcoming_appointments || []);
            }
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    /**
     * Render upcoming appointments
     */
    renderUpcomingAppointments(appointments) {
        const container = document.getElementById('upcomingAppointments');
        
        if (appointments.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Không có lịch hẹn sắp tới</p>';
            return;
        }
        
        const html = appointments.map(appointment => `
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                <div>
                    <div class="fw-bold">${appointment.patient_name}</div>
                    <small class="text-muted">${appointment.appointment_date} ${appointment.appointment_time}</small>
                </div>
                <span class="badge bg-primary">${appointment.service_type}</span>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }

    /**
     * Load Patients page
     */
    async loadPatients() {
        if (!this.pageModules.patients) {
            this.pageModules.patients = new PatientsModule();
        }
        await this.pageModules.patients.render();
    }

    /**
     * Load Appointments page
     */
    async loadAppointments() {
        if (!this.pageModules.appointments) {
            this.pageModules.appointments = new AppointmentsModule();
        }
        await this.pageModules.appointments.render();
    }

    /**
     * Load Reports page
     */
    async loadReports() {
        if (!this.pageModules.reports) {
            this.pageModules.reports = new ReportsModule();
        }
        await this.pageModules.reports.render();
    }

    /**
     * Refresh dashboard
     */
    async refreshDashboard() {
        if (this.currentPage === 'dashboard') {
            await this.loadDashboardData();
            api.showSuccess('Đã làm mới dữ liệu');
        }
    }

    /**
     * Show page loading
     */
    showPageLoading(show) {
        const content = document.getElementById('pageContent');
        if (show) {
            content.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                    <div class="mt-2">Đang tải...</div>
                </div>
            `;
        }
    }

    /**
     * Utility methods
     */
    formatStatus(status) {
        const statusMap = {
            'dat_lich': 'Đặt lịch',
            'xac_nhan': 'Xác nhận',
            'dang_kham': 'Đang khám',
            'hoan_thanh': 'Hoàn thành',
            'huy_lich': 'Hủy lịch',
            'khong_den': 'Không đến'
        };
        return statusMap[status] || status;
    }

    formatServiceType(type) {
        const typeMap = {
            'kham_tong_quat': 'Khám tổng quát',
            'tay_trang': 'Tẩy trắng',
            'nho_rang': 'Nhổ răng',
            'han_tram': 'Hàn trám',
            'dieu_tri_tuy': 'Điều trị tủy',
            'nieng_rang': 'Niềng răng',
            'cay_ghep': 'Cấy ghép',
            'phau_thuat': 'Phẫu thuật',
            'tu_van': 'Tư vấn'
        };
        return typeMap[type] || type;
    }
}

// Create global app instance
window.app = new App();

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    app.init();
});
