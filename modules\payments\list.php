<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

// Xử lý tham số
$status = isset($_GET['status']) ? $_GET['status'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

// Xây dựng điều kiện WHERE
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(b.hoten LIKE ? OR b.sdt LIKE ? OR a.id LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
}

if (!empty($status)) {
    $where_conditions[] = "a.payment_status = ?";
    $params[] = $status;
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

// Đếm tổng số bản ghi
$count_sql = "SELECT COUNT(*) as total
              FROM appointments a
              LEFT JOIN benhnhan b ON a.patient_id = b.id
              $where_clause";
$count_result = $database->fetchOne($count_sql, $params);
$total_records = $count_result ? $count_result['total'] : 0;
$total_pages = ceil($total_records / $limit);

// Lấy danh sách thanh toán
$sql = "SELECT a.id, a.appointment_date, a.appointment_time, a.service_type,
               a.estimated_cost, a.actual_cost, a.payment_status, a.status as appointment_status,
               b.hoten as patient_name, b.sdt as patient_phone,
               a.created_at, a.updated_at
        FROM appointments a 
        LEFT JOIN benhnhan b ON a.patient_id = b.id 
        $where_clause 
        ORDER BY a.appointment_date DESC, a.appointment_time DESC 
        LIMIT $limit OFFSET $offset";

$payments = $database->fetchAll($sql, $params);

// Thống kê nhanh
$stats = [];
$stats['total_revenue'] = $database->fetchOne("SELECT SUM(actual_cost) as total FROM appointments WHERE payment_status = 'da_thanh_toan'")['total'] ?: 0;
$stats['pending_amount'] = $database->fetchOne("SELECT SUM(estimated_cost) as total FROM appointments WHERE payment_status = 'chua_thanh_toan' AND status = 'hoan_thanh'")['total'] ?: 0;
$stats['today_revenue'] = $database->fetchOne("SELECT SUM(actual_cost) as total FROM appointments WHERE payment_status = 'da_thanh_toan' AND appointment_date = CURDATE()")['total'] ?: 0;

function getPaymentStatusBadge($status) {
    $badges = [
        'chua_thanh_toan' => '<span style="background: #ff9800; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">💳 Chưa thanh toán</span>',
        'da_thanh_toan' => '<span style="background: #4caf50; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">✅ Đã thanh toán</span>',
        'thanh_toan_mot_phan' => '<span style="background: #2196f3; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">📊 Thanh toán một phần</span>'
    ];
    return $badges[$status] ?? $status;
}

function getServiceTypeLabel($service_type) {
    $labels = [
        'kham_tong_quat' => '🔍 Khám tổng quát',
        'tay_trang' => '✨ Tẩy trắng',
        'nho_rang' => '🦷 Nhổ răng',
        'han_tram' => '🔧 Hàn trám',
        'dieu_tri_tuy' => '🩺 Điều trị tủy',
        'nieng_rang' => '📐 Niềng răng',
        'cay_ghep' => '🔩 Cấy ghép',
        'phau_thuat' => '⚕️ Phẫu thuật',
        'tu_van' => '💬 Tư vấn'
    ];
    return $labels[$service_type] ?? '🔍 Khám tổng quát';
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💰 Quản lý Thanh toán</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/home-button.css">
    <style>
        .payments-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card.pending {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }
        
        .stat-card.today {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .filter-box {
            display: grid;
            grid-template-columns: 2fr 1fr auto;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
        }
        
        .payments-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .payments-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .payments-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }
        
        .payments-table tr:hover {
            background: #f8f9fa;
        }
        
        .amount {
            font-weight: bold;
            font-size: 16px;
        }
        
        .amount.estimated {
            color: #666;
        }
        
        .amount.actual {
            color: #4CAF50;
        }
        
        .amount.pending {
            color: #FF9800;
        }
        
        @media (max-width: 768px) {
            .filter-box {
                grid-template-columns: 1fr;
            }
            
            .payments-table {
                font-size: 14px;
            }
            
            .payments-table th,
            .payments-table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="../../dashboard.php" class="home-btn">
        <span style="font-size: 20px;">🏠</span>
        <span>Trang chủ</span>
    </a>

    <div class="payments-container fade-in">
        <div class="module-header">
            <h1>
                <span style="font-size: 40px;">💰</span>
                Quản lý Thanh toán
            </h1>
            <a href="add.php" class="btn btn-primary">+ Ghi nhận thanh toán</a>
        </div>

        <!-- Thống kê nhanh -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number"><?php echo number_format($stats['total_revenue'], 0, ',', '.'); ?>đ</div>
                <div class="stat-label">💰 Tổng doanh thu</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-number"><?php echo number_format($stats['pending_amount'], 0, ',', '.'); ?>đ</div>
                <div class="stat-label">⏳ Chờ thanh toán</div>
            </div>
            <div class="stat-card today">
                <div class="stat-number"><?php echo number_format($stats['today_revenue'], 0, ',', '.'); ?>đ</div>
                <div class="stat-label">📅 Doanh thu hôm nay</div>
            </div>
        </div>

        <!-- Bộ lọc -->
        <form method="GET" class="filter-box">
            <div class="form-group">
                <label>Tìm kiếm</label>
                <input type="text" name="search" placeholder="Tên bệnh nhân, SĐT, mã lịch hẹn..." 
                       value="<?php echo htmlspecialchars($search); ?>">
            </div>
            
            <div class="form-group">
                <label>Trạng thái thanh toán</label>
                <select name="status">
                    <option value="">-- Tất cả --</option>
                    <option value="chua_thanh_toan" <?php echo ($status == 'chua_thanh_toan') ? 'selected' : ''; ?>>💳 Chưa thanh toán</option>
                    <option value="da_thanh_toan" <?php echo ($status == 'da_thanh_toan') ? 'selected' : ''; ?>>✅ Đã thanh toán</option>
                    <option value="thanh_toan_mot_phan" <?php echo ($status == 'thanh_toan_mot_phan') ? 'selected' : ''; ?>>📊 Thanh toán một phần</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-primary">🔍 Lọc</button>
        </form>

        <!-- Bảng thanh toán -->
        <div style="overflow-x: auto;">
            <table class="payments-table">
                <thead>
                    <tr>
                        <th>Mã LH</th>
                        <th>Bệnh nhân</th>
                        <th>Dịch vụ</th>
                        <th>Ngày khám</th>
                        <th>Chi phí dự kiến</th>
                        <th>Chi phí thực tế</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($payments)): ?>
                        <?php foreach ($payments as $payment): ?>
                            <tr>
                                <td>
                                    <strong>LH<?php echo str_pad($payment['id'], 4, '0', STR_PAD_LEFT); ?></strong>
                                </td>
                                <td>
                                    <div class="patient-name"><?php echo htmlspecialchars($payment['patient_name']); ?></div>
                                    <small>📞 <?php echo htmlspecialchars($payment['patient_phone']); ?></small>
                                </td>
                                <td><?php echo getServiceTypeLabel($payment['service_type']); ?></td>
                                <td>
                                    <strong><?php echo date('d/m/Y', strtotime($payment['appointment_date'])); ?></strong>
                                    <br><small><?php echo date('H:i', strtotime($payment['appointment_time'])); ?></small>
                                </td>
                                <td>
                                    <div class="amount estimated">
                                        <?php echo $payment['estimated_cost'] ? number_format($payment['estimated_cost'], 0, ',', '.') . 'đ' : 'Chưa xác định'; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="amount <?php echo $payment['actual_cost'] ? 'actual' : 'pending'; ?>">
                                        <?php echo $payment['actual_cost'] ? number_format($payment['actual_cost'], 0, ',', '.') . 'đ' : 'Chưa thanh toán'; ?>
                                    </div>
                                </td>
                                <td><?php echo getPaymentStatusBadge($payment['payment_status']); ?></td>
                                <td>
                                    <?php if ($payment['payment_status'] != 'da_thanh_toan'): ?>
                                        <a href="process.php?id=<?php echo $payment['id']; ?>" 
                                           class="btn btn-primary" style="padding: 6px 10px; font-size: 12px;">💳 Thanh toán</a>
                                    <?php endif; ?>
                                    <a href="receipt.php?id=<?php echo $payment['id']; ?>" 
                                       class="btn btn-secondary" style="padding: 6px 10px; font-size: 12px;">🧾 Biên lai</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                                <div style="font-size: 48px; margin-bottom: 10px;">💰</div>
                                <div>Không có dữ liệu thanh toán nào</div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Phân trang -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status); ?>" 
                       class="<?php echo ($i == $page) ? 'active' : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.payments-container').classList.add('fade-in');
        });
    </script>
</body>
</html>
