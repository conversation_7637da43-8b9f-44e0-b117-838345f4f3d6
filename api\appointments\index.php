<?php
/**
 * API Appointments Endpoints
 * GET /api/appointments - List appointments with pagination and filters
 * POST /api/appointments - Create new appointment
 */

require_once '../config/cors.php';
require_once '../config/response.php';
require_once '../config/middleware.php';
require_once '../../config/database.php';

$database = new Database();

// Authenticate user
$current_user = ApiMiddleware::authenticate();

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        
        case 'GET':
            // List appointments with pagination and filters
            $pagination = ApiMiddleware::validatePagination();
            $search = ApiMiddleware::parseSearch();
            
            // Additional filters
            $status = isset($_GET['status']) ? trim($_GET['status']) : '';
            $date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
            $date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
            
            // Build query
            $where_conditions = [];
            $params = [];
            
            if (!empty($search)) {
                $where_conditions[] = "(b.hoten LIKE ? OR b.sdt LIKE ? OR a.reason LIKE ?)";
                $search_param = "%$search%";
                $params = array_merge($params, [$search_param, $search_param, $search_param]);
            }
            
            if (!empty($status)) {
                $where_conditions[] = "a.status = ?";
                $params[] = $status;
            }
            
            if (!empty($date_from)) {
                $where_conditions[] = "a.appointment_date >= ?";
                $params[] = $date_from;
            }
            
            if (!empty($date_to)) {
                $where_conditions[] = "a.appointment_date <= ?";
                $params[] = $date_to;
            }
            
            $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
            
            // Count total records
            $count_sql = "SELECT COUNT(*) as total 
                         FROM appointments a 
                         LEFT JOIN benhnhan b ON a.patient_id = b.id 
                         $where_clause";
            $total = $database->count($count_sql, $params);
            
            // Get appointments
            $sql = "SELECT a.id, a.appointment_date, a.appointment_time, a.duration, 
                           a.reason, a.service_type, a.priority, a.status, a.notes,
                           a.estimated_cost, a.actual_cost, a.payment_status,
                           b.hoten as patient_name, b.sdt as patient_phone, b.id as patient_id
                    FROM appointments a 
                    LEFT JOIN benhnhan b ON a.patient_id = b.id 
                    $where_clause 
                    ORDER BY a.appointment_date DESC, a.appointment_time DESC 
                    LIMIT {$pagination['limit']} OFFSET {$pagination['offset']}";
            
            $appointments = $database->fetchAll($sql, $params);
            
            // Format dates and times
            foreach ($appointments as &$appointment) {
                $appointment['appointment_date'] = date('d/m/Y', strtotime($appointment['appointment_date']));
                $appointment['appointment_time'] = date('H:i', strtotime($appointment['appointment_time']));
                $appointment['estimated_cost'] = $appointment['estimated_cost'] ? number_format($appointment['estimated_cost']) : null;
                $appointment['actual_cost'] = $appointment['actual_cost'] ? number_format($appointment['actual_cost']) : null;
            }
            
            ApiResponse::paginated(
                $appointments, 
                $total, 
                $pagination['page'], 
                $pagination['limit'],
                'Lấy danh sách lịch hẹn thành công'
            );
            break;
            
        case 'POST':
            // Create new appointment
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                ApiResponse::error('Invalid JSON input', 400);
            }
            
            // Validate required fields
            ApiResponse::validateRequired($input, ['patient_id', 'appointment_date', 'appointment_time']);
            
            // Sanitize input
            $data = ApiResponse::sanitize($input);
            
            // Validate patient exists
            $patient = $database->fetchOne(
                "SELECT id FROM benhnhan WHERE id = ?", 
                [$data['patient_id']]
            );
            
            if (!$patient) {
                ApiResponse::error('Bệnh nhân không tồn tại', 400);
            }
            
            // Check for time conflicts
            $conflict = $database->fetchOne(
                "SELECT id FROM appointments 
                 WHERE appointment_date = ? AND appointment_time = ? AND status != 'huy_lich'", 
                [$data['appointment_date'], $data['appointment_time']]
            );
            
            if ($conflict) {
                ApiResponse::error('Thời gian này đã có lịch hẹn khác', 400);
            }
            
            // Insert appointment
            $sql = "INSERT INTO appointments (patient_id, appointment_date, appointment_time, duration,
                                            reason, service_type, priority, notes, estimated_cost, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $data['patient_id'],
                $data['appointment_date'],
                $data['appointment_time'],
                $data['duration'] ?? 30,
                $data['reason'] ?? '',
                $data['service_type'] ?? 'kham_tong_quat',
                $data['priority'] ?? 'binh_thuong',
                $data['notes'] ?? '',
                $data['estimated_cost'] ?? null,
                $current_user['id']
            ];
            
            if ($database->execute($sql, $params)) {
                $appointment_id = $database->lastInsertId();
                
                // Get created appointment with patient info
                $new_appointment = $database->fetchOne(
                    "SELECT a.*, b.hoten as patient_name, b.sdt as patient_phone
                     FROM appointments a 
                     LEFT JOIN benhnhan b ON a.patient_id = b.id 
                     WHERE a.id = ?", 
                    [$appointment_id]
                );
                
                ApiResponse::success($new_appointment, 'Đặt lịch hẹn thành công', 201);
            } else {
                ApiResponse::error('Lỗi khi đặt lịch hẹn', 500);
            }
            break;
            
        default:
            ApiResponse::error('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("API Appointments Error: " . $e->getMessage());
    ApiResponse::error('Lỗi hệ thống', 500);
}
?>
