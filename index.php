<?php
// B<PERSON>t đầu session tr<PERSON>ớ<PERSON> khi include auth
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once 'includes/auth.php';

$auth = new Auth();
$error = '';
$success = '';

// Ki<PERSON>m tra nếu đã đăng nhập
if ($auth->isLoggedIn()) {
    header("Location: dashboard.php");
    exit();
}

// Kiểm tra thông báo logout
if (isset($_GET['message']) && $_GET['message'] == 'logout_success') {
    $success = "Đăng xuất thành công!";
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    if (empty($username) || empty($password)) {
        $error = "Vui lòng nhập đầy đủ thông tin!";
    } else {
        $result = $auth->login($username, $password);

        if ($result['success']) {
            header("Location: dashboard.php");
            exit();
        } else {
            $error = $result['message'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đăng nhập - Phòng khám</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/icons.css">
</head>
<body>
<div class="login-container">
    <div class="welcome-section">
        <div class="icon-replacement medical-welcome">⚕️</div>
        <h1>Hệ thống Quản lý Phòng khám</h1>
    </div>

    <div class="login-form">
        <h2>Đăng nhập</h2>
        <form method="POST" id="loginForm">
            <div class="input-group">
                <input name="username" placeholder="Tài khoản" required>
            </div>
            <div class="input-group">
                <input name="password" type="password" placeholder="Mật khẩu" required>
            </div>
            <button type="submit" class="login-btn">
                <span class="btn-text">Đăng nhập</span>
                <div class="loading-icon" style="display: none;"></div>
            </button>
        </form>
    </div>
</div>
<?php if (!empty($error)) echo "<div class='error-message'>$error</div>"; ?>
<?php if (!empty($success)) echo "<div class='success-message' style='background: #d4edda; color: #155724; padding: 15px; border-radius: 10px; margin-top: 20px; border-left: 4px solid #28a745; text-align: center; font-weight: 600;'>$success</div>"; ?>

<script>
document.getElementById('loginForm').addEventListener('submit', function() {
    const btnText = document.querySelector('.btn-text');
    const loadingGif = document.querySelector('.loading-gif');
    const loginBtn = document.querySelector('.login-btn');

    // Hiển thị loading
    btnText.style.display = 'none';
    loadingGif.style.display = 'inline-block';
    loginBtn.disabled = true;
    loginBtn.style.opacity = '0.7';
});

// Animation cho trang
document.addEventListener('DOMContentLoaded', function() {
    document.querySelector('.login-container').classList.add('fade-in');
});
</script>
</body>
</html>
