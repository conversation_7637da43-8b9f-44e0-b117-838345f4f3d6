<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';
require_once '../../includes/navigation-buttons.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

// Kiểm tra quyền admin
if ($current_user['role'] !== 'dentist') {
    header("Location: ../../dashboard.php?error=access_denied");
    exit();
}

$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$user_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

// Lấy thông tin user
$sql = "SELECT * FROM users WHERE id = ?";
$user = $database->fetchOne($sql, [$user_id]);

if (!$user) {
    header("Location: list.php?error=user_not_found");
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'update_profile') {
        $email = trim($_POST['email']);
        $full_name = trim($_POST['full_name']);
        $role = $_POST['role'];
        $phone = trim($_POST['phone']);
        $address = trim($_POST['address']);
        $clinic_name = trim($_POST['clinic_name']);
        $clinic_address = trim($_POST['clinic_address']);
        $clinic_phone = trim($_POST['clinic_phone']);
        $license_number = trim($_POST['license_number']);
        $specialty = trim($_POST['specialty']);
        $experience_years = (int)($_POST['experience_years'] ?? 1);
        $education = trim($_POST['education']);
        $bio = trim($_POST['bio']);
        
        // Validation
        if (empty($full_name) || empty($role)) {
            $error = "Vui lòng điền đầy đủ thông tin bắt buộc!";
        } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "Email không hợp lệ!";
        } else {
            try {
                // Kiểm tra email trùng (nếu có và khác email hiện tại)
                if (!empty($email) && $email !== $user['email']) {
                    $check_email = $database->fetchOne("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $user_id]);
                    if ($check_email) {
                        $error = "Email đã được sử dụng bởi tài khoản khác!";
                    }
                }
                
                if (empty($error)) {
                    // Update user
                    $update_sql = "UPDATE users SET 
                        email = ?, full_name = ?, role = ?, phone = ?, address = ?,
                        clinic_name = ?, clinic_address = ?, clinic_phone = ?, license_number = ?,
                        specialty = ?, experience_years = ?, education = ?, bio = ?, updated_at = NOW()
                        WHERE id = ?";
                    
                    $params = [
                        $email, $full_name, $role, $phone, $address,
                        $clinic_name, $clinic_address, $clinic_phone, $license_number,
                        $specialty, $experience_years, $education, $bio, $user_id
                    ];
                    
                    if ($database->execute($update_sql, $params)) {
                        $success = "Cập nhật thông tin thành công!";
                        // Lấy lại thông tin mới
                        $user = $database->fetchOne($sql, [$user_id]);
                        
                        // Cập nhật session nếu đang sửa chính mình
                        if ($user_id == $current_user['id']) {
                            $_SESSION['full_name'] = $full_name;
                            $_SESSION['email'] = $email;
                            $_SESSION['phone'] = $phone;
                            $_SESSION['role'] = $role;
                        }
                    } else {
                        $error = "Lỗi khi cập nhật thông tin!";
                    }
                }
            } catch(Exception $e) {
                $error = "Lỗi hệ thống: " . $e->getMessage();
            }
        }
    } elseif ($action == 'change_password') {
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        // Validation
        if (empty($new_password)) {
            $error = "Vui lòng nhập mật khẩu mới!";
        } elseif (strlen($new_password) < 6) {
            $error = "Mật khẩu phải có ít nhất 6 ký tự!";
        } elseif ($new_password !== $confirm_password) {
            $error = "Mật khẩu xác nhận không khớp!";
        } else {
            try {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $update_sql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
                
                if ($database->execute($update_sql, [$hashed_password, $user_id])) {
                    $success = "Đổi mật khẩu thành công!";
                } else {
                    $error = "Lỗi khi đổi mật khẩu!";
                }
            } catch(Exception $e) {
                $error = "Lỗi hệ thống: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✏️ Chỉnh sửa Người dùng</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/navigation-buttons.css">
    <style>
        .edit-user-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .user-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
            margin: 0 auto 15px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #eee;
            margin-bottom: 30px;
        }
        
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .tab.active {
            border-bottom-color: #4CAF50;
            color: #4CAF50;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 5px solid #4CAF50;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .form-row.single {
            grid-template-columns: 1fr;
        }
        
        .role-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .role-card {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .role-card:hover {
            border-color: #4CAF50;
            background: #f8f9fa;
        }
        
        .role-card.selected {
            border-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .role-card input[type="radio"] {
            display: none;
        }
        
        .role-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .role-name {
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .role-desc {
            font-size: 12px;
            color: #666;
        }
        
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }
        
        .strength-weak { color: #f44336; }
        .strength-medium { color: #ff9800; }
        .strength-strong { color: #4caf50; }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .role-cards {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <?php renderNavigationButtons('list.php'); ?>

    <div class="edit-user-container fade-in">
        <!-- Thông báo -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <!-- Header -->
        <div class="user-header">
            <div class="user-avatar">
                <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
            </div>
            <h2><?php echo htmlspecialchars($user['full_name']); ?></h2>
            <p>@<?php echo htmlspecialchars($user['username']); ?></p>
            <p style="opacity: 0.8; font-size: 14px;">
                <?php 
                $role_names = [
                    'dentist' => '👨‍⚕️ Bác sĩ Nha khoa',
                    'assistant' => '👩‍💼 Trợ lý Nha khoa'
                ];
                echo $role_names[$user['role']] ?? $user['role'];
                ?>
            </p>
        </div>

        <!-- Tabs -->
        <div class="tabs">
            <div class="tab active" data-tab="profile">
                <span>👤</span> Thông tin cá nhân
            </div>
            <div class="tab" data-tab="password">
                <span>🔐</span> Đổi mật khẩu
            </div>
        </div>

        <!-- Tab Content: Profile -->
        <div class="tab-content active" id="profile">
            <form method="POST">
                <input type="hidden" name="action" value="update_profile">
                
                <!-- Thông tin cơ bản -->
                <div class="form-section">
                    <div class="section-title">
                        <span>👤</span>
                        Thông tin Cơ bản
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="full_name">Họ và tên *</label>
                            <input type="text" id="full_name" name="full_name" required 
                                   value="<?php echo htmlspecialchars($user['full_name']); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($user['email']); ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone">Số điện thoại</label>
                            <input type="tel" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($user['phone']); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label>Username</label>
                            <input type="text" value="<?php echo htmlspecialchars($user['username']); ?>" 
                                   disabled style="background: #f5f5f5;">
                            <small style="color: #666;">Username không thể thay đổi</small>
                        </div>
                    </div>
                    
                    <div class="form-row single">
                        <div class="form-group">
                            <label for="address">Địa chỉ</label>
                            <textarea id="address" name="address" rows="2"><?php echo htmlspecialchars($user['address']); ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Vai trò -->
                <div class="form-section">
                    <div class="section-title">
                        <span>🎭</span>
                        Vai trò trong Hệ thống
                    </div>
                    
                    <div class="role-cards">
                        <label class="role-card <?php echo ($user['role'] == 'dentist') ? 'selected' : ''; ?>" for="role_dentist">
                            <input type="radio" id="role_dentist" name="role" value="dentist" 
                                   <?php echo ($user['role'] == 'dentist') ? 'checked' : ''; ?>>
                            <div class="role-icon">👨‍⚕️</div>
                            <div class="role-name">Bác sĩ Nha khoa</div>
                            <div class="role-desc">Quyền quản lý toàn bộ hệ thống</div>
                        </label>
                        
                        <label class="role-card <?php echo ($user['role'] == 'assistant') ? 'selected' : ''; ?>" for="role_assistant">
                            <input type="radio" id="role_assistant" name="role" value="assistant" 
                                   <?php echo ($user['role'] == 'assistant') ? 'checked' : ''; ?>>
                            <div class="role-icon">👩‍💼</div>
                            <div class="role-name">Trợ lý Nha khoa</div>
                            <div class="role-desc">Hỗ trợ quản lý lịch hẹn và bệnh nhân</div>
                        </label>
                    </div>
                </div>

                <!-- Thông tin chuyên môn -->
                <div class="form-section" id="professionalInfo" style="<?php echo ($user['role'] != 'dentist') ? 'display: none;' : ''; ?>">
                    <div class="section-title">
                        <span>🏥</span>
                        Thông tin Chuyên môn
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="clinic_name">Tên phòng khám</label>
                            <input type="text" id="clinic_name" name="clinic_name" 
                                   value="<?php echo htmlspecialchars($user['clinic_name']); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="clinic_phone">SĐT phòng khám</label>
                            <input type="tel" id="clinic_phone" name="clinic_phone" 
                                   value="<?php echo htmlspecialchars($user['clinic_phone']); ?>">
                        </div>
                    </div>
                    
                    <div class="form-row single">
                        <div class="form-group">
                            <label for="clinic_address">Địa chỉ phòng khám</label>
                            <textarea id="clinic_address" name="clinic_address" rows="2"><?php echo htmlspecialchars($user['clinic_address']); ?></textarea>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="license_number">Số chứng chỉ hành nghề</label>
                            <input type="text" id="license_number" name="license_number" 
                                   value="<?php echo htmlspecialchars($user['license_number']); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="specialty">Chuyên khoa</label>
                            <input type="text" id="specialty" name="specialty" 
                                   value="<?php echo htmlspecialchars($user['specialty']); ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="experience_years">Số năm kinh nghiệm</label>
                            <input type="number" id="experience_years" name="experience_years" min="0" max="50"
                                   value="<?php echo htmlspecialchars($user['experience_years']); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="education">Trình độ học vấn</label>
                            <input type="text" id="education" name="education" 
                                   value="<?php echo htmlspecialchars($user['education']); ?>">
                        </div>
                    </div>
                    
                    <div class="form-row single">
                        <div class="form-group">
                            <label for="bio">Giới thiệu</label>
                            <textarea id="bio" name="bio" rows="3"><?php echo htmlspecialchars($user['bio']); ?></textarea>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary" style="padding: 15px 30px; font-size: 16px;">
                        💾 Cập nhật thông tin
                    </button>
                </div>
            </form>
        </div>

        <!-- Tab Content: Password -->
        <div class="tab-content" id="password">
            <form method="POST">
                <input type="hidden" name="action" value="change_password">
                
                <div class="form-section">
                    <div class="section-title">
                        <span>🔐</span>
                        Đổi Mật khẩu
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="new_password">Mật khẩu mới *</label>
                            <input type="password" id="new_password" name="new_password" required 
                                   placeholder="Mật khẩu mới (ít nhất 6 ký tự)">
                            <div id="passwordStrength" class="password-strength"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Xác nhận mật khẩu *</label>
                            <input type="password" id="confirm_password" name="confirm_password" required 
                                   placeholder="Nhập lại mật khẩu mới">
                            <div id="passwordMatch" class="password-strength"></div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary" style="padding: 15px 30px; font-size: 16px;">
                        🔑 Đổi mật khẩu
                    </button>
                </div>
            </form>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="list.php" class="btn btn-secondary" style="padding: 12px 24px;">
                ← Quay lại danh sách
            </a>
            <a href="view.php?id=<?php echo $user['id']; ?>" class="btn btn-info" style="padding: 12px 24px; margin-left: 10px;">
                👁️ Xem chi tiết
            </a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.edit-user-container').classList.add('fade-in');
            
            // Tab switching
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;
                    
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(tc => tc.classList.remove('active'));
                    
                    this.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
            
            // Role selection handling
            const roleCards = document.querySelectorAll('.role-card');
            const professionalInfo = document.getElementById('professionalInfo');
            
            roleCards.forEach(card => {
                card.addEventListener('click', function() {
                    roleCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    
                    const role = this.querySelector('input[type="radio"]').value;
                    if (role === 'dentist') {
                        professionalInfo.style.display = 'block';
                    } else {
                        professionalInfo.style.display = 'none';
                    }
                });
            });
            
            // Password strength checker
            const passwordInput = document.getElementById('new_password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const strengthDiv = document.getElementById('passwordStrength');
            const matchDiv = document.getElementById('passwordMatch');
            
            if (passwordInput) {
                passwordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    let message = '';
                    
                    if (password.length >= 6) strength++;
                    if (password.match(/[a-z]/)) strength++;
                    if (password.match(/[A-Z]/)) strength++;
                    if (password.match(/[0-9]/)) strength++;
                    if (password.match(/[^a-zA-Z0-9]/)) strength++;
                    
                    if (password.length === 0) {
                        message = '';
                    } else if (strength < 2) {
                        message = '❌ Mật khẩu yếu';
                        strengthDiv.className = 'password-strength strength-weak';
                    } else if (strength < 4) {
                        message = '⚠️ Mật khẩu trung bình';
                        strengthDiv.className = 'password-strength strength-medium';
                    } else {
                        message = '✅ Mật khẩu mạnh';
                        strengthDiv.className = 'password-strength strength-strong';
                    }
                    
                    strengthDiv.textContent = message;
                    checkPasswordMatch();
                });
                
                confirmPasswordInput.addEventListener('input', checkPasswordMatch);
                
                function checkPasswordMatch() {
                    const password = passwordInput.value;
                    const confirmPassword = confirmPasswordInput.value;
                    
                    if (confirmPassword.length === 0) {
                        matchDiv.textContent = '';
                    } else if (password === confirmPassword) {
                        matchDiv.textContent = '✅ Mật khẩu khớp';
                        matchDiv.className = 'password-strength strength-strong';
                    } else {
                        matchDiv.textContent = '❌ Mật khẩu không khớp';
                        matchDiv.className = 'password-strength strength-weak';
                    }
                }
            }
        });
    </script>
</body>
</html>
