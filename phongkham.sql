-- =====================================================
-- DATABASE: HỆ THỐNG QUẢN LÝ PHÒNG KHÁM NHA KHOA TƯ NHÂN
-- Mục tiêu: G<PERSON><PERSON><PERSON> bác sĩ nha khoa tư nhân quản lý lịch hẹn và tư vấn khách hàng
-- Phiên bản: 2.0 - Tối ưu hóa cho bác sĩ tư nhân
-- =====================================================

CREATE DATABASE IF NOT EXISTS phongkham CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE phongkham;

-- =====================================================
-- BẢNG NGƯỜI DÙNG HỆ THỐNG (Đơn giản hóa cho bác sĩ tư nhân)
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    full_name VARCHAR(100) NOT NULL,
    role ENUM('dentist', 'assistant') DEFAULT 'dentist',
    phone VARCHAR(20),
    address TEXT,
    avatar VARCHAR(255),
    clinic_name VARCHAR(200) DEFAULT 'Phòng khám nha khoa tư nhân',
    clinic_address TEXT,
    clinic_phone VARCHAR(20),
    license_number VARCHAR(50),
    specialty VARCHAR(100) DEFAULT 'Nha khoa tổng quát',
    experience_years INT DEFAULT 1,
    education TEXT,
    bio TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- BẢNG KHÁCH HÀNG (Bệnh nhân) - Tối ưu cho nha khoa
-- =====================================================
CREATE TABLE IF NOT EXISTS benhnhan (
    id INT AUTO_INCREMENT PRIMARY KEY,
    hoten VARCHAR(100) NOT NULL,
    sdt VARCHAR(20),
    email VARCHAR(100),
    diachi TEXT,
    ngaysinh DATE,
    gioitinh ENUM('Nam', 'Nữ', 'Khác') DEFAULT 'Nam',
    cmnd VARCHAR(20),
    nghe_nghiep VARCHAR(100),

    -- Thông tin y tế nha khoa
    tien_su_benh TEXT COMMENT 'Tiền sử bệnh tổng quát',
    tien_su_nha_khoa TEXT COMMENT 'Tiền sử bệnh nha khoa',
    di_ung TEXT COMMENT 'Dị ứng thuốc, vật liệu nha khoa',
    thuoc_dang_dung TEXT COMMENT 'Thuốc đang sử dụng',
    benh_ly_hien_tai TEXT COMMENT 'Bệnh lý hiện tại',

    -- Thông tin liên hệ khẩn cấp
    lien_he_khan_cap VARCHAR(200) COMMENT 'Người liên hệ khẩn cấp',
    sdt_khan_cap VARCHAR(20) COMMENT 'SĐT khẩn cấp',

    -- Ghi chú và theo dõi
    ghi_chu TEXT COMMENT 'Ghi chú chung',
    lan_kham_cuoi DATE COMMENT 'Lần khám cuối',
    trang_thai_dieu_tri ENUM('moi', 'dang_dieu_tri', 'hoan_thanh', 'ngung_dieu_tri') DEFAULT 'moi',

    -- Thông tin hệ thống
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Index để tìm kiếm nhanh
    INDEX idx_hoten (hoten),
    INDEX idx_sdt (sdt),
    INDEX idx_email (email),
    INDEX idx_ngaysinh (ngaysinh)
);

-- =====================================================
-- BẢNG LỊCH HẸN - Tối ưu cho bác sĩ tư nhân
-- =====================================================
CREATE TABLE IF NOT EXISTS appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration INT DEFAULT 30 COMMENT 'Thời gian khám (phút)',

    -- Thông tin lịch hẹn
    reason TEXT COMMENT 'Lý do khám',
    service_type ENUM('kham_tong_quat', 'tay_trang', 'nho_rang', 'han_tram', 'dieu_tri_tuy', 'nieng_rang', 'cay_ghep', 'phau_thuat', 'tu_van') DEFAULT 'kham_tong_quat',
    priority ENUM('binh_thuong', 'khan_cap', 'uu_tien') DEFAULT 'binh_thuong',

    -- Trạng thái và theo dõi
    status ENUM('dat_lich', 'xac_nhan', 'dang_kham', 'hoan_thanh', 'huy_lich', 'khong_den') DEFAULT 'dat_lich',
    notes TEXT COMMENT 'Ghi chú của bác sĩ',
    patient_notes TEXT COMMENT 'Ghi chú của bệnh nhân',

    -- Thông tin thanh toán
    estimated_cost DECIMAL(10,2) COMMENT 'Chi phí dự kiến',
    actual_cost DECIMAL(10,2) COMMENT 'Chi phí thực tế',
    payment_status ENUM('chua_thanh_toan', 'da_thanh_toan', 'thanh_toan_mot_phan') DEFAULT 'chua_thanh_toan',

    -- Lịch hẹn tiếp theo
    next_appointment_date DATE COMMENT 'Lịch hẹn tiếp theo',
    follow_up_needed BOOLEAN DEFAULT FALSE COMMENT 'Cần tái khám',

    -- Thông tin hệ thống
    created_by INT DEFAULT 1 COMMENT 'Người tạo lịch hẹn',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign keys và indexes
    FOREIGN KEY (patient_id) REFERENCES benhnhan(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),

    INDEX idx_appointment_date (appointment_date),
    INDEX idx_appointment_time (appointment_time),
    INDEX idx_status (status),
    INDEX idx_patient_date (patient_id, appointment_date)
);

-- =====================================================
-- BẢNG HỒ SƠ KHÁM BỆNH - Đơn giản cho nha khoa tư nhân
-- =====================================================
CREATE TABLE IF NOT EXISTS medical_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    appointment_id INT,
    visit_date DATE NOT NULL,

    -- Thông tin khám bệnh
    chief_complaint TEXT COMMENT 'Lý do đến khám',
    symptoms TEXT COMMENT 'Triệu chứng',
    clinical_examination TEXT COMMENT 'Khám lâm sàng',
    diagnosis TEXT COMMENT 'Chẩn đoán',
    treatment_plan TEXT COMMENT 'Kế hoạch điều trị',
    treatment_performed TEXT COMMENT 'Điều trị đã thực hiện',

    -- Thông tin nha khoa cụ thể
    teeth_condition TEXT COMMENT 'Tình trạng răng',
    gum_condition TEXT COMMENT 'Tình trạng nướu',
    oral_hygiene_score ENUM('tot', 'trung_binh', 'kem') COMMENT 'Điểm vệ sinh răng miệng',

    -- Thuốc và hướng dẫn
    medications TEXT COMMENT 'Thuốc kê đơn',
    instructions TEXT COMMENT 'Hướng dẫn chăm sóc',

    -- Theo dõi
    follow_up_date DATE COMMENT 'Ngày tái khám',
    follow_up_notes TEXT COMMENT 'Ghi chú tái khám',

    -- Chi phí
    treatment_cost DECIMAL(10,2) COMMENT 'Chi phí điều trị',

    -- Hệ thống
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (patient_id) REFERENCES benhnhan(id) ON DELETE CASCADE,
    FOREIGN KEY (appointment_id) REFERENCES appointments(id),

    INDEX idx_patient_date (patient_id, visit_date),
    INDEX idx_visit_date (visit_date)
);

-- =====================================================
-- BẢNG LỊCH LÀM VIỆC - Đơn giản cho bác sĩ tư nhân
-- =====================================================
CREATE TABLE IF NOT EXISTS doctor_schedules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    day_of_week ENUM('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
    start_time TIME DEFAULT '08:00:00',
    end_time TIME DEFAULT '17:00:00',
    lunch_start TIME DEFAULT '12:00:00',
    lunch_end TIME DEFAULT '13:00:00',
    is_available BOOLEAN DEFAULT TRUE,
    max_patients_per_day INT DEFAULT 20,
    appointment_duration INT DEFAULT 30 COMMENT 'Thời gian mỗi lịch hẹn (phút)',
    notes TEXT COMMENT 'Ghi chú lịch làm việc',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- =====================================================
-- DỮ LIỆU MẪU CHO BÁC SĨ NHA KHOA TƯ NHÂN
-- =====================================================

-- Thêm bác sĩ nha khoa tư nhân (mật khẩu đã được hash)
INSERT INTO users (username, password, email, full_name, role, phone, address, clinic_name, clinic_address, clinic_phone, license_number, specialty, experience_years, education, bio) VALUES
('dr.nam', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'BS. Nguyễn Văn Nam', 'dentist', '**********', '123 Đường ABC, Quận 1, TP.HCM', 'Phòng khám nha khoa Dr. Nam', '123 Đường ABC, Quận 1, TP.HCM', '028-1234-5678', 'NK001234', 'Nha khoa tổng quát', 8, 'Bác sĩ Răng Hàm Mặt - Đại học Y Dược TP.HCM', 'Bác sĩ nha khoa với 8 năm kinh nghiệm, chuyên về điều trị tổng quát và thẩm mỹ nha khoa'),
('assistant', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'Trần Thị Mai', 'assistant', '**********', '123 Đường ABC, Quận 1, TP.HCM', 'Phòng khám nha khoa Dr. Nam', '123 Đường ABC, Quận 1, TP.HCM', '028-1234-5678', '', 'Hỗ trợ', 2, 'Trung cấp Y tế', 'Trợ lý nha khoa, hỗ trợ bác sĩ trong các thủ thuật');

-- Thêm khách hàng mẫu với thông tin nha khoa
INSERT INTO benhnhan (hoten, sdt, email, diachi, ngaysinh, gioitinh, cmnd, nghe_nghiep, tien_su_benh, tien_su_nha_khoa, di_ung, lien_he_khan_cap, sdt_khan_cap, ghi_chu, trang_thai_dieu_tri) VALUES
('Nguyễn Văn Anh', '**********', '<EMAIL>', '123 Đường Lê Lợi, Quận 1, TP.HCM', '1985-05-15', 'Nam', '123456789', 'Kỹ sư phần mềm', 'Không có bệnh lý đặc biệt', 'Đã nhổ răng khôn năm 2020', 'Dị ứng Penicillin', 'Nguyễn Thị Lan (vợ)', '0901234568', 'Khách hàng thân thiết, hay đến muộn 10 phút', 'moi'),

('Lê Thị Bình', '**********', '<EMAIL>', '456 Đường Nguyễn Huệ, Quận 1, TP.HCM', '1990-08-20', 'Nữ', '987654321', 'Giáo viên tiểu học', 'Tiểu đường type 2', 'Niềng răng từ 2018-2020', 'Không có', 'Lê Văn Cường (chồng)', '0912345679', 'Cần chú ý đường huyết khi điều trị', 'dang_dieu_tri'),

('Trần Văn Cường', '0923456789', '<EMAIL>', '789 Đường Pasteur, Quận 3, TP.HCM', '1988-12-10', 'Nam', '456789123', 'Bác sĩ tim mạch', 'Cao huyết áp', 'Cấy ghép implant răng số 6 hàm dưới', 'Dị ứng Lidocaine', 'Trần Thị Mai (vợ)', '0923456790', 'Đồng nghiệp, ưu tiên lịch hẹn', 'dang_dieu_tri'),

('Phạm Thị Dung', '0934567890', '<EMAIL>', '321 Đường Cách Mạng Tháng 8, Quận 10, TP.HCM', '1992-03-25', 'Nữ', '789123456', 'Nhân viên văn phòng', 'Không có', 'Tẩy trắng răng 6 tháng trước', 'Dị ứng thuốc tê', 'Phạm Văn Hùng (anh trai)', '0934567891', 'Sợ đau, cần an ủi nhiều', 'hoan_thanh'),

('Hoàng Minh Tuấn', '**********', '<EMAIL>', '654 Đường Võ Văn Tần, Quận 3, TP.HCM', '1995-11-30', 'Nam', '654321987', 'Nhân viên marketing', 'Hen suyễn nhẹ', 'Chưa từng điều trị nha khoa', 'Không có', 'Hoàng Thị Nga (mẹ)', '**********', 'Khách hàng mới, cần tư vấn kỹ', 'moi');

-- Thêm lịch làm việc cho bác sĩ tư nhân
INSERT INTO doctor_schedules (day_of_week, start_time, end_time, lunch_start, lunch_end, is_available, max_patients_per_day, appointment_duration, notes) VALUES
('Monday', '08:00:00', '17:00:00', '12:00:00', '13:00:00', TRUE, 16, 30, 'Thứ 2 - Ngày bắt đầu tuần, thường đông khách'),
('Tuesday', '08:00:00', '17:00:00', '12:00:00', '13:00:00', TRUE, 16, 30, 'Thứ 3 - Ngày bình thường'),
('Wednesday', '08:00:00', '17:00:00', '12:00:00', '13:00:00', TRUE, 16, 30, 'Thứ 4 - Ngày bình thường'),
('Thursday', '08:00:00', '17:00:00', '12:00:00', '13:00:00', TRUE, 16, 30, 'Thứ 5 - Ngày bình thường'),
('Friday', '08:00:00', '17:00:00', '12:00:00', '13:00:00', TRUE, 16, 30, 'Thứ 6 - Ngày cuối tuần, có thể đông'),
('Saturday', '08:00:00', '12:00:00', '10:00:00', '10:15:00', TRUE, 8, 30, 'Thứ 7 - Chỉ làm buổi sáng'),
('Sunday', '00:00:00', '00:00:00', '00:00:00', '00:00:00', FALSE, 0, 30, 'Chủ nhật - Nghỉ');

-- Thêm một số lịch hẹn mẫu
INSERT INTO appointments (patient_id, appointment_date, appointment_time, duration, reason, service_type, priority, status, notes, estimated_cost, payment_status, created_by) VALUES
(1, '2024-12-20', '09:00:00', 30, 'Đau răng số 6 hàm trên bên phải', 'kham_tong_quat', 'binh_thuong', 'dat_lich', 'Khách hàng cũ, đã từng điều trị', 200000, 'chua_thanh_toan', 1),
(2, '2024-12-20', '10:00:00', 60, 'Tái khám sau cấy ghép implant', 'kham_tong_quat', 'uu_tien', 'xac_nhan', 'Cần kiểm tra quá trình lành vết thương', 150000, 'chua_thanh_toan', 1),
(3, '2024-12-21', '14:00:00', 45, 'Tư vấn niềng răng cho con', 'tu_van', 'binh_thuong', 'dat_lich', 'Tư vấn cho con gái 12 tuổi', 100000, 'chua_thanh_toan', 1),
(4, '2024-12-22', '08:30:00', 90, 'Hàn trám răng số 4, 5 hàm dưới', 'han_tram', 'binh_thuong', 'dat_lich', 'Sâu răng nhiều vị trí', 400000, 'chua_thanh_toan', 1),
(5, '2024-12-23', '15:30:00', 30, 'Khám tổng quát định kỳ', 'kham_tong_quat', 'binh_thuong', 'dat_lich', 'Khách hàng mới, cần khám toàn diện', 200000, 'chua_thanh_toan', 1);

-- Thêm một số hồ sơ khám bệnh mẫu
INSERT INTO medical_records (patient_id, appointment_id, visit_date, chief_complaint, symptoms, clinical_examination, diagnosis, treatment_plan, treatment_performed, teeth_condition, gum_condition, oral_hygiene_score, medications, instructions, treatment_cost) VALUES
(1, 1, '2024-12-15', 'Đau răng khi ăn đồ lạnh', 'Đau nhói khi tiếp xúc với thức ăn lạnh, đau tăng vào ban đêm', 'Răng số 6 hàm trên phải có lỗ sâu sâu, test lạnh (+), test gõ (+)', 'Sâu răng sâu - Viêm tủy không hồi phục', 'Điều trị tủy răng số 6 hàm trên phải', 'Đã lấy tủy, làm sạch ống tủy, hẹn lần sau trám tạm', 'Răng số 6 sâu sâu, các răng khác ổn định', 'Nướu hơi sưng quanh răng số 6', 'trung_binh', 'Thuốc giảm đau Ibuprofen 400mg x 2 viên/ngày', 'Súc miệng nước muối, tránh nhai bên phải', 800000),

(2, 2, '2024-12-16', 'Tái khám sau cấy ghép implant', 'Không đau, vết thương lành tốt', 'Vị trí cấy ghép implant răng số 6 hàm dưới lành tốt, không sưng, không đau', 'Implant ổn định tốt', 'Tiếp tục theo dõi, hẹn lắp crown sau 2 tháng', 'Kiểm tra độ ổn định implant, chụp X-quang kiểm tra', 'Implant ổn định, xương hàm lành tốt', 'Nướu quanh implant hồng, không sưng', 'tot', 'Không cần thuốc', 'Vệ sinh răng miệng tốt, dùng chỉ nha khoa', 150000);

-- =====================================================
-- CẤU HÌNH BẢO MẬT VÀ TỐI ƯU HÓA
-- =====================================================

-- Tạo view để bảo mật thông tin nhạy cảm
CREATE VIEW patient_summary AS
SELECT
    id,
    hoten,
    sdt,
    email,
    ngaysinh,
    gioitinh,
    trang_thai_dieu_tri,
    lan_kham_cuoi,
    created_at
FROM benhnhan;

-- Tạo view cho lịch hẹn hôm nay
CREATE VIEW today_appointments AS
SELECT
    a.id,
    a.appointment_time,
    a.duration,
    a.reason,
    a.service_type,
    a.status,
    a.priority,
    b.hoten as patient_name,
    b.sdt as patient_phone,
    a.estimated_cost
FROM appointments a
JOIN benhnhan b ON a.patient_id = b.id
WHERE a.appointment_date = CURDATE()
ORDER BY a.appointment_time;

-- Tạo view thống kê nhanh
CREATE VIEW clinic_stats AS
SELECT
    (SELECT COUNT(*) FROM benhnhan) as total_patients,
    (SELECT COUNT(*) FROM appointments WHERE appointment_date = CURDATE()) as today_appointments,
    (SELECT COUNT(*) FROM appointments WHERE appointment_date = CURDATE() AND status = 'hoan_thanh') as completed_today,
    (SELECT SUM(actual_cost) FROM appointments WHERE appointment_date = CURDATE() AND payment_status = 'da_thanh_toan') as today_revenue;

-- =====================================================
-- THÔNG TIN ĐĂNG NHẬP MẶC ĐỊNH
-- =====================================================
/*
THÔNG TIN ĐĂNG NHẬP:

1. Bác sĩ chính:
   - Username: dr.nam
   - Password: 123456
   - Role: dentist
2. Bác sĩ chính:
   - Username: admin
   - Password: 123456
   - Role: dentist
2. Trợ lý:
   - Username: assistant
   - Password: 123456
   - Role: assistant

LƯU Ý:
- Mật khẩu đã được hash bằng bcrypt
- Đổi mật khẩu ngay sau khi cài đặt
- Cấu hình backup định kỳ
- Kiểm tra quyền truy cập database

HƯỚNG DẪN SỬ DỤNG:
1. Import file SQL này vào MySQL
2. Đăng nhập bằng tài khoản dr.nam/password
3. Cập nhật thông tin phòng khám trong profile
4. Thêm khách hàng và đặt lịch hẹn
5. Sử dụng calendar để xem lịch theo tháng

TÍNH NĂNG CHÍNH:
✅ Quản lý khách hàng (CRUD)
✅ Quản lý lịch hẹn (CRUD)
✅ Lịch làm việc bác sĩ
✅ Hồ sơ khám bệnh
✅ Thống kê cơ bản
✅ Giao diện responsive
✅ Bảo mật tốt

PHIÊN BẢN: 2.0 - Tối ưu cho bác sĩ nha khoa tư nhân
NGÀY CẬP NHẬT: December 2024
*/
