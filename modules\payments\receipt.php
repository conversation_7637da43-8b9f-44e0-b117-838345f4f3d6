<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

$appointment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$appointment_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

// Lấy thông tin lịch hẹn và thanh toán
$sql = "SELECT a.*, b.hoten as patient_name, b.sdt as patient_phone, 
               b.email as patient_email, b.diachi as patient_address
        FROM appointments a 
        LEFT JOIN benhnhan b ON a.patient_id = b.id 
        WHERE a.id = ?";

$appointment = $database->fetchOne($sql, [$appointment_id]);

if (!$appointment) {
    header("Location: list.php?error=not_found");
    exit();
}

function getServiceTypeLabel($service_type) {
    $labels = [
        'kham_tong_quat' => 'Khám tổng quát',
        'tay_trang' => 'Tẩy trắng răng',
        'nho_rang' => 'Nhổ răng',
        'han_tram' => 'Hàn trám răng',
        'dieu_tri_tuy' => 'Điều trị tủy răng',
        'nieng_rang' => 'Niềng răng',
        'cay_ghep' => 'Cấy ghép implant',
        'phau_thuat' => 'Phẫu thuật nha khoa',
        'tu_van' => 'Tư vấn nha khoa'
    ];
    return $labels[$service_type] ?? 'Khám tổng quát';
}

function getPaymentMethodLabel($method) {
    $methods = [
        'tien_mat' => 'Tiền mặt',
        'chuyen_khoan' => 'Chuyển khoản',
        'the_ngan_hang' => 'Thẻ ngân hàng',
        'khac' => 'Phương thức khác'
    ];
    return $methods[$method] ?? 'Tiền mặt';
}

function getPaymentStatusLabel($status) {
    $statuses = [
        'da_thanh_toan' => 'Đã thanh toán đầy đủ',
        'thanh_toan_mot_phan' => 'Thanh toán một phần',
        'chua_thanh_toan' => 'Chưa thanh toán'
    ];
    return $statuses[$status] ?? 'Chưa thanh toán';
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧾 Biên lai Thanh toán - LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        body {
            background: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .receipt-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .clinic-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .clinic-info {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .receipt-body {
            padding: 30px;
        }
        
        .receipt-title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .info-section {
            margin-bottom: 25px;
        }
        
        .info-title {
            font-size: 16px;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 15px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 5px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
        }
        
        .info-value {
            font-weight: 500;
            color: #333;
            text-align: right;
        }
        
        .payment-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        
        .total-amount {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }
        
        .total-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .total-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .receipt-footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
        }
        
        .print-buttons {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        @media print {
            body {
                background: white;
            }
            
            .print-buttons {
                display: none;
            }
            
            .receipt-container {
                box-shadow: none;
                margin: 0;
            }
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .receipt-container {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <div class="clinic-name">
                <?php echo htmlspecialchars($current_user['clinic_name'] ?? 'Phòng khám Nha khoa'); ?>
            </div>
            <div class="clinic-info">
                <?php if (!empty($current_user['clinic_address'])): ?>
                    📍 <?php echo htmlspecialchars($current_user['clinic_address']); ?><br>
                <?php endif; ?>
                <?php if (!empty($current_user['clinic_phone'])): ?>
                    📞 <?php echo htmlspecialchars($current_user['clinic_phone']); ?><br>
                <?php endif; ?>
                <?php if (!empty($current_user['email'])): ?>
                    ✉️ <?php echo htmlspecialchars($current_user['email']); ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Body -->
        <div class="receipt-body">
            <div class="receipt-title">🧾 Biên lai Thanh toán</div>
            
            <!-- Thông tin cơ bản -->
            <div class="info-section">
                <div class="info-title">📋 Thông tin cơ bản</div>
                <div class="info-grid">
                    <div>
                        <div class="info-item">
                            <span class="info-label">Mã lịch hẹn:</span>
                            <span class="info-value">LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Ngày khám:</span>
                            <span class="info-value"><?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Giờ khám:</span>
                            <span class="info-value"><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></span>
                        </div>
                    </div>
                    <div>
                        <div class="info-item">
                            <span class="info-label">Ngày in biên lai:</span>
                            <span class="info-value"><?php echo date('d/m/Y H:i'); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Người lập:</span>
                            <span class="info-value"><?php echo htmlspecialchars($current_user['full_name']); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Thông tin bệnh nhân -->
            <div class="info-section">
                <div class="info-title">👤 Thông tin bệnh nhân</div>
                <div class="info-grid">
                    <div>
                        <div class="info-item">
                            <span class="info-label">Họ và tên:</span>
                            <span class="info-value"><?php echo htmlspecialchars($appointment['patient_name']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Số điện thoại:</span>
                            <span class="info-value"><?php echo htmlspecialchars($appointment['patient_phone']); ?></span>
                        </div>
                    </div>
                    <div>
                        <?php if (!empty($appointment['patient_email'])): ?>
                        <div class="info-item">
                            <span class="info-label">Email:</span>
                            <span class="info-value"><?php echo htmlspecialchars($appointment['patient_email']); ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($appointment['patient_address'])): ?>
                        <div class="info-item">
                            <span class="info-label">Địa chỉ:</span>
                            <span class="info-value"><?php echo htmlspecialchars($appointment['patient_address']); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Thông tin dịch vụ -->
            <div class="info-section">
                <div class="info-title">🦷 Thông tin dịch vụ</div>
                <div class="info-item">
                    <span class="info-label">Dịch vụ thực hiện:</span>
                    <span class="info-value"><?php echo getServiceTypeLabel($appointment['service_type']); ?></span>
                </div>
                <?php if (!empty($appointment['reason'])): ?>
                <div class="info-item">
                    <span class="info-label">Lý do khám:</span>
                    <span class="info-value"><?php echo htmlspecialchars($appointment['reason']); ?></span>
                </div>
                <?php endif; ?>
                <?php if (!empty($appointment['notes'])): ?>
                <div class="info-item">
                    <span class="info-label">Ghi chú:</span>
                    <span class="info-value"><?php echo htmlspecialchars($appointment['notes']); ?></span>
                </div>
                <?php endif; ?>
            </div>

            <!-- Thông tin thanh toán -->
            <div class="payment-summary">
                <div class="info-title">💰 Chi tiết thanh toán</div>
                <div class="info-item">
                    <span class="info-label">Chi phí dự kiến:</span>
                    <span class="info-value">
                        <?php echo $appointment['estimated_cost'] ? number_format($appointment['estimated_cost'], 0, ',', '.') . 'đ' : 'Chưa xác định'; ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Chi phí thực tế:</span>
                    <span class="info-value">
                        <?php echo $appointment['actual_cost'] ? number_format($appointment['actual_cost'], 0, ',', '.') . 'đ' : '0đ'; ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Phương thức thanh toán:</span>
                    <span class="info-value">
                        <?php echo getPaymentMethodLabel($appointment['payment_method'] ?? 'tien_mat'); ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Trạng thái:</span>
                    <span class="info-value"><?php echo getPaymentStatusLabel($appointment['payment_status']); ?></span>
                </div>
                <?php if (!empty($appointment['payment_notes'])): ?>
                <div class="info-item">
                    <span class="info-label">Ghi chú thanh toán:</span>
                    <span class="info-value"><?php echo htmlspecialchars($appointment['payment_notes']); ?></span>
                </div>
                <?php endif; ?>
            </div>

            <!-- Tổng tiền -->
            <div class="total-amount">
                <div class="total-number">
                    <?php echo number_format($appointment['actual_cost'] ?: 0, 0, ',', '.'); ?>đ
                </div>
                <div class="total-label">Tổng số tiền đã thanh toán</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="receipt-footer">
            <p><strong>Cảm ơn quý khách đã sử dụng dịch vụ!</strong></p>
            <p>Biên lai này là bằng chứng thanh toán hợp lệ. Vui lòng giữ lại để đối chiếu khi cần thiết.</p>
            <p style="margin-top: 15px; font-style: italic;">
                Mọi thắc mắc xin liên hệ: <?php echo htmlspecialchars($current_user['clinic_phone'] ?? $current_user['phone'] ?? ''); ?>
            </p>
        </div>
    </div>

    <!-- Print buttons -->
    <div class="print-buttons">
        <button onclick="window.print()" class="btn btn-primary" style="padding: 12px 24px; margin-right: 10px;">
            🖨️ In biên lai
        </button>
        <a href="list.php" class="btn btn-secondary" style="padding: 12px 24px;">
            ← Quay lại danh sách
        </a>
    </div>

    <script>
        // Auto print when URL has print parameter
        if (window.location.search.includes('print=1')) {
            window.onload = function() {
                window.print();
            };
        }
    </script>
</body>
</html>
