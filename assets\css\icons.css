/* ========================================
   ICON REPLACEMENTS - CSS ICONS
   Thay thế GIF bằng CSS icons animated
======================================== */

/* Base icon class */
.icon-replacement {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    position: relative;
    overflow: hidden;
}

/* Medical Welcome Icon - Trang đăng nhập */
.medical-welcome {
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    font-size: 60px;
    animation: pulse 2s infinite;
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
    border: 4px solid rgba(255, 255, 255, 0.2);
}

.medical-welcome::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

/* Dashboard Icon - Dashboard header */
.dashboard-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    font-size: 40px;
    animation: bounce 2s infinite;
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.2);
}

/* Patient Icon - Menu bệnh nhân */
.patient-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    font-size: 20px;
    margin-right: 10px;
    vertical-align: middle;
    animation: gentle-pulse 3s infinite;
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

/* Loading Spinner - Buttons */
.loading-icon {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    background: transparent;
}

/* Loading Spinner Alternative - Dots */
.loading-dots {
    display: inline-flex;
    gap: 4px;
    align-items: center;
}

.loading-dots::before,
.loading-dots::after,
.loading-dots {
    content: '';
    width: 6px;
    height: 6px;
    background: #4CAF50;
    border-radius: 50%;
    animation: loading-dots 1.4s infinite ease-in-out;
}

.loading-dots::before {
    animation-delay: -0.32s;
}

.loading-dots::after {
    animation-delay: -0.16s;
}

/* Logout Icon - Menu đăng xuất */
.logout-icon {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    font-size: 16px;
    animation: gentle-pulse 3s infinite;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

/* Success Icon */
.success-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    font-size: 30px;
    animation: success-pop 0.6s ease-out;
}

/* Error Icon */
.error-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    font-size: 30px;
    animation: error-shake 0.6s ease-out;
}

/* Warning Icon */
.warning-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    font-size: 30px;
    animation: warning-wobble 0.6s ease-out;
}

/* Info Icon */
.info-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    font-size: 30px;
    animation: info-bounce 0.6s ease-out;
}

/* ========================================
   ANIMATIONS
======================================== */

/* Pulse Animation */
@keyframes pulse {
    0% { 
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
    }
    50% { 
        transform: scale(1.05);
        box-shadow: 0 15px 40px rgba(76, 175, 80, 0.4);
    }
    100% { 
        transform: scale(1);
        box-shadow: 0 10px 30px rgba(76, 175, 80, 0.3);
    }
}

/* Gentle Pulse */
@keyframes gentle-pulse {
    0%, 100% { 
        transform: scale(1);
        opacity: 1;
    }
    50% { 
        transform: scale(1.02);
        opacity: 0.9;
    }
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { 
        transform: translateY(0);
    }
    40% { 
        transform: translateY(-8px);
    }
    60% { 
        transform: translateY(-4px);
    }
}

/* Spin Animation */
@keyframes spin {
    0% { 
        transform: rotate(0deg);
    }
    100% { 
        transform: rotate(360deg);
    }
}

/* Shine Effect */
@keyframes shine {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
    50% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
    100% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }
}

/* Loading Dots */
@keyframes loading-dots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Success Pop */
@keyframes success-pop {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Error Shake */
@keyframes error-shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Warning Wobble */
@keyframes warning-wobble {
    0% {
        transform: rotate(0deg);
    }
    15% {
        transform: rotate(-5deg);
    }
    30% {
        transform: rotate(5deg);
    }
    45% {
        transform: rotate(-3deg);
    }
    60% {
        transform: rotate(3deg);
    }
    75% {
        transform: rotate(-1deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

/* Info Bounce */
@keyframes info-bounce {
    0% {
        transform: translateY(0);
    }
    25% {
        transform: translateY(-10px);
    }
    50% {
        transform: translateY(0);
    }
    75% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
    }
}

/* ========================================
   RESPONSIVE DESIGN
======================================== */

@media (max-width: 768px) {
    .medical-welcome {
        width: 120px;
        height: 120px;
        font-size: 48px;
    }
    
    .dashboard-icon {
        width: 80px;
        height: 80px;
        font-size: 32px;
    }
    
    .patient-icon {
        width: 35px;
        height: 35px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .medical-welcome {
        width: 100px;
        height: 100px;
        font-size: 40px;
    }
    
    .dashboard-icon {
        width: 70px;
        height: 70px;
        font-size: 28px;
    }
    
    .patient-icon {
        width: 30px;
        height: 30px;
        font-size: 16px;
    }
}

/* ========================================
   UTILITY CLASSES
======================================== */

/* Icon sizes */
.icon-xs { width: 16px; height: 16px; font-size: 10px; }
.icon-sm { width: 24px; height: 24px; font-size: 14px; }
.icon-md { width: 32px; height: 32px; font-size: 18px; }
.icon-lg { width: 48px; height: 48px; font-size: 24px; }
.icon-xl { width: 64px; height: 64px; font-size: 32px; }

/* Icon colors */
.icon-primary { background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); }
.icon-success { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }
.icon-warning { background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%); }
.icon-danger { background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%); }
.icon-info { background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%); }

/* No animation */
.icon-static {
    animation: none !important;
}

/* Hover effects */
.icon-hover:hover {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}

/* Print styles */
@media print {
    .icon-replacement {
        background: #666 !important;
        color: white !important;
        animation: none !important;
    }
}
