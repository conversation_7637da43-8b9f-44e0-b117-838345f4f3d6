<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';
require_once '../../includes/navigation-buttons.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

// Kiểm tra quyền admin (chỉ dentist mới có quyền quản lý user)
if ($current_user['role'] !== 'dentist') {
    header("Location: ../../dashboard.php?error=access_denied");
    exit();
}

// Xử lý tham số
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$role_filter = isset($_GET['role']) ? $_GET['role'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Xây dựng điều kiện WHERE
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR username LIKE ? OR email LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param]);
}

if (!empty($role_filter)) {
    $where_conditions[] = "role = ?";
    $params[] = $role_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

// Đếm tổng số bản ghi
$count_sql = "SELECT COUNT(*) as total FROM users $where_clause";
$count_result = $database->fetchOne($count_sql, $params);
$total_records = $count_result ? $count_result['total'] : 0;
$total_pages = ceil($total_records / $limit);

// Lấy danh sách users
$sql = "SELECT id, username, email, full_name, role, phone, status, 
               clinic_name, specialty, experience_years, created_at, updated_at
        FROM users 
        $where_clause 
        ORDER BY created_at DESC 
        LIMIT $limit OFFSET $offset";

$users = $database->fetchAll($sql, $params);

// Thống kê nhanh
$stats = [];
$stats['total_users'] = $database->fetchOne("SELECT COUNT(*) as count FROM users")['count'];
$stats['active_users'] = $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count'];
$stats['dentists'] = $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'dentist'")['count'];
$stats['assistants'] = $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'assistant'")['count'];

function getRoleBadge($role) {
    $badges = [
        'dentist' => '<span style="background: #4CAF50; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">👨‍⚕️ Bác sĩ</span>',
        'assistant' => '<span style="background: #2196F3; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">👩‍💼 Trợ lý</span>',
        'admin' => '<span style="background: #FF9800; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">👑 Admin</span>'
    ];
    return $badges[$role] ?? $role;
}

function getStatusBadge($status) {
    $badges = [
        'active' => '<span style="background: #4CAF50; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">✅ Hoạt động</span>',
        'inactive' => '<span style="background: #f44336; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">❌ Tạm khóa</span>'
    ];
    return $badges[$status] ?? $status;
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👥 Quản lý Người dùng</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/navigation-buttons.css">
    <style>
        .users-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card.blue {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        
        .stat-card.orange {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }
        
        .stat-card.purple {
            background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .filter-box {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr auto;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
        }
        
        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .users-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .users-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }
        
        .users-table tr:hover {
            background: #f8f9fa;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 6px 10px;
            font-size: 12px;
            border-radius: 6px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        
        @media (max-width: 768px) {
            .filter-box {
                grid-template-columns: 1fr;
            }
            
            .users-table {
                font-size: 14px;
            }
            
            .users-table th,
            .users-table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <?php renderNavigationButtons(); ?>

    <div class="users-container fade-in">
        <!-- Thông báo -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($_GET['success']); ?></div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-error">
                <?php
                $error_messages = [
                    'access_denied' => 'Bạn không có quyền truy cập chức năng này!',
                    'invalid_id' => 'ID người dùng không hợp lệ!',
                    'user_not_found' => 'Không tìm thấy người dùng!',
                    'cannot_lock_self' => 'Bạn không thể khóa tài khoản của chính mình!',
                    'update_failed' => 'Cập nhật thất bại!'
                ];
                echo $error_messages[$_GET['error']] ?? htmlspecialchars($_GET['error']);
                ?>
            </div>
        <?php endif; ?>

        <div class="module-header">
            <h1>
                <span style="font-size: 40px;">👥</span>
                Quản lý Người dùng
            </h1>
            <a href="add.php" class="btn btn-primary">+ Thêm người dùng</a>
        </div>

        <!-- Thống kê nhanh -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                <div class="stat-label">👥 Tổng người dùng</div>
            </div>
            <div class="stat-card blue">
                <div class="stat-number"><?php echo $stats['active_users']; ?></div>
                <div class="stat-label">✅ Đang hoạt động</div>
            </div>
            <div class="stat-card orange">
                <div class="stat-number"><?php echo $stats['dentists']; ?></div>
                <div class="stat-label">👨‍⚕️ Bác sĩ</div>
            </div>
            <div class="stat-card purple">
                <div class="stat-number"><?php echo $stats['assistants']; ?></div>
                <div class="stat-label">👩‍💼 Trợ lý</div>
            </div>
        </div>

        <!-- Bộ lọc -->
        <form method="GET" class="filter-box">
            <div class="form-group">
                <label>Tìm kiếm</label>
                <input type="text" name="search" placeholder="Tên, username, email..." 
                       value="<?php echo htmlspecialchars($search); ?>">
            </div>
            
            <div class="form-group">
                <label>Vai trò</label>
                <select name="role">
                    <option value="">-- Tất cả --</option>
                    <option value="dentist" <?php echo ($role_filter == 'dentist') ? 'selected' : ''; ?>>👨‍⚕️ Bác sĩ</option>
                    <option value="assistant" <?php echo ($role_filter == 'assistant') ? 'selected' : ''; ?>>👩‍💼 Trợ lý</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Trạng thái</label>
                <select name="status">
                    <option value="">-- Tất cả --</option>
                    <option value="active" <?php echo ($status_filter == 'active') ? 'selected' : ''; ?>>✅ Hoạt động</option>
                    <option value="inactive" <?php echo ($status_filter == 'inactive') ? 'selected' : ''; ?>>❌ Tạm khóa</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-primary">🔍 Lọc</button>
        </form>

        <!-- Bảng người dùng -->
        <div style="overflow-x: auto;">
            <table class="users-table">
                <thead>
                    <tr>
                        <th>Avatar</th>
                        <th>Thông tin</th>
                        <th>Vai trò</th>
                        <th>Liên hệ</th>
                        <th>Trạng thái</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($users)): ?>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="user-avatar">
                                        <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
                                    </div>
                                </td>
                                <td>
                                    <div style="font-weight: bold; margin-bottom: 4px;">
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </div>
                                    <div style="color: #666; font-size: 14px;">
                                        @<?php echo htmlspecialchars($user['username']); ?>
                                    </div>
                                    <?php if (!empty($user['clinic_name'])): ?>
                                        <div style="color: #888; font-size: 12px; margin-top: 2px;">
                                            🏥 <?php echo htmlspecialchars($user['clinic_name']); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo getRoleBadge($user['role']); ?>
                                    <?php if (!empty($user['specialty'])): ?>
                                        <div style="color: #666; font-size: 12px; margin-top: 4px;">
                                            <?php echo htmlspecialchars($user['specialty']); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($user['email'])): ?>
                                        <div style="margin-bottom: 4px;">
                                            📧 <?php echo htmlspecialchars($user['email']); ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (!empty($user['phone'])): ?>
                                        <div>
                                            📞 <?php echo htmlspecialchars($user['phone']); ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo getStatusBadge($user['status']); ?></td>
                                <td>
                                    <div><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></div>
                                    <div style="color: #666; font-size: 12px;">
                                        <?php echo date('H:i', strtotime($user['created_at'])); ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="view.php?id=<?php echo $user['id']; ?>" 
                                           class="btn btn-secondary btn-small" title="Xem chi tiết">
                                            👁️ Xem
                                        </a>
                                        <a href="edit.php?id=<?php echo $user['id']; ?>" 
                                           class="btn btn-primary btn-small" title="Chỉnh sửa">
                                            ✏️ Sửa
                                        </a>
                                        <?php if ($user['id'] != $current_user['id']): ?>
                                            <a href="toggle-status.php?id=<?php echo $user['id']; ?>" 
                                               class="btn btn-warning btn-small" 
                                               title="<?php echo ($user['status'] == 'active') ? 'Khóa tài khoản' : 'Kích hoạt tài khoản'; ?>"
                                               onclick="return confirm('Bạn có chắc muốn thay đổi trạng thái tài khoản này?')">
                                                <?php echo ($user['status'] == 'active') ? '🔒 Khóa' : '🔓 Mở'; ?>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                                <div style="font-size: 48px; margin-bottom: 10px;">👥</div>
                                <div>Không có người dùng nào</div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Phân trang -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role_filter); ?>&status=<?php echo urlencode($status_filter); ?>" 
                       class="<?php echo ($i == $page) ? 'active' : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.users-container').classList.add('fade-in');
        });
    </script>
</body>
</html>
