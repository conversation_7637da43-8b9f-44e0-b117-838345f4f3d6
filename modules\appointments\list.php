<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();

// X<PERSON> lý tìm kiếm và lọc
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? trim($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : '';
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : '';
$dentist_id = isset($_GET['dentist_id']) ? (int)$_GET['dentist_id'] : 0;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

// Xây dựng câu truy vấn
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(b.hoten LIKE ? OR b.sdt LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param]);
}

if (!empty($status)) {
    $where_conditions[] = "a.status = ?";
    $params[] = $status;
}

if (!empty($date_from)) {
    $where_conditions[] = "a.appointment_date >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "a.appointment_date <= ?";
    $params[] = $date_to;
}

// Loại bỏ filter dentist_id vì không có bảng dentists
// Bác sĩ tư nhân không cần filter theo dentist

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

$database = new Database();

// Đếm tổng số bản ghi với database mới
$count_sql = "SELECT COUNT(*) as total
              FROM appointments a
              LEFT JOIN benhnhan b ON a.patient_id = b.id
              $where_clause";
$count_result = $database->fetchOne($count_sql, $params);
$total_records = $count_result ? $count_result['total'] : 0;
$total_pages = ceil($total_records / $limit);

// Lấy danh sách lịch hẹn với database mới
$sql = "SELECT a.*,
               b.hoten as patient_name,
               b.sdt as patient_phone,
               b.email as patient_email,
               a.service_type,
               CASE
                   WHEN a.appointment_date = CURDATE() THEN 'Hôm nay'
                   WHEN a.appointment_date = DATE_ADD(CURDATE(), INTERVAL 1 DAY) THEN 'Ngày mai'
                   WHEN a.appointment_date < CURDATE() THEN 'Quá hạn'
                   ELSE DATE_FORMAT(a.appointment_date, '%d/%m/%Y')
               END as date_display
        FROM appointments a
        LEFT JOIN benhnhan b ON a.patient_id = b.id
        $where_clause
        ORDER BY a.appointment_date DESC, a.appointment_time DESC
        LIMIT $limit OFFSET $offset";
$appointments = $database->fetchAll($sql, $params);

// Đảm bảo $appointments là array
if (!is_array($appointments)) {
    $appointments = [];
}

// Không cần lấy danh sách bác sĩ vì không có bảng dentists
$dentists = [];

// Xử lý thông báo
$success = '';
$error = '';
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'added':
            $success = "Đặt lịch hẹn thành công!";
            break;
        case 'updated':
            $success = "Cập nhật lịch hẹn thành công!";
            break;
        case 'cancelled':
            $success = "Hủy lịch hẹn thành công!";
            break;
    }
}

// Hàm hiển thị trạng thái với status mới
function getStatusBadge($status) {
    $badges = [
        'dat_lich' => '<span style="background: #ffc107; color: #000; padding: 4px 8px; border-radius: 12px; font-size: 12px;">📅 Đã đặt</span>',
        'xac_nhan' => '<span style="background: #17a2b8; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">✅ Xác nhận</span>',
        'dang_kham' => '<span style="background: #007bff; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">🔄 Đang khám</span>',
        'hoan_thanh' => '<span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">✅ Hoàn thành</span>',
        'huy_lich' => '<span style="background: #dc3545; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">❌ Đã hủy</span>',
        'khong_den' => '<span style="background: #6c757d; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">👻 Không đến</span>'
    ];
    return $badges[$status] ?? '<span style="background: #6c757d; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">' . $status . '</span>';
}

function getPriorityBadge($priority) {
    $badges = [
        'binh_thuong' => '<span style="background: #e9ecef; color: #495057; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Bình thường</span>',
        'khan_cap' => '<span style="background: #fff3cd; color: #856404; padding: 2px 6px; border-radius: 8px; font-size: 11px;">⚡ Khẩn cấp</span>',
        'uu_tien' => '<span style="background: #f8d7da; color: #721c24; padding: 2px 6px; border-radius: 8px; font-size: 11px;">🚨 Ưu tiên</span>'
    ];
    return $badges[$priority] ?? '<span style="background: #e9ecef; color: #495057; padding: 2px 6px; border-radius: 8px; font-size: 11px;">' . $priority . '</span>';
}

function getServiceTypeLabel($service_type) {
    $labels = [
        'kham_tong_quat' => '🔍 Khám tổng quát',
        'tay_trang' => '✨ Tẩy trắng',
        'nho_rang' => '🦷 Nhổ răng',
        'han_tram' => '🔧 Hàn trám',
        'dieu_tri_tuy' => '🩺 Điều trị tủy',
        'nieng_rang' => '📐 Niềng răng',
        'cay_ghep' => '🔩 Cấy ghép',
        'phau_thuat' => '⚕️ Phẫu thuật',
        'tu_van' => '💬 Tư vấn'
    ];
    return $labels[$service_type] ?? '🔍 Khám tổng quát';
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý Lịch hẹn - Phòng khám Nha khoa</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .module-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin: 20px auto;
            max-width: 1400px;
        }
        
        .module-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .filter-box {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 15px;
            margin-bottom: 20px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group select {
            padding: 10px;
            border: 2px solid #e1e1e1;
            border-radius: 8px;
            font-size: 14px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 2px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
        
        .appointments-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }
        
        .appointments-table th,
        .appointments-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }
        
        .appointments-table th {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            font-weight: 600;
        }
        
        .appointments-table tr:hover {
            background: #f8f9fa;
        }
        
        .appointment-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #4CAF50;
        }
        
        .appointment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .appointment-code {
            font-weight: bold;
            color: #4CAF50;
        }
        
        .appointment-time {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        
        .patient-info {
            margin-bottom: 10px;
        }
        
        .patient-name {
            font-weight: 600;
            color: #333;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .home-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
            background: linear-gradient(45deg, #ee5a52, #ff6b6b);
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        @media (max-width: 768px) {
            .filter-box {
                grid-template-columns: 1fr;
            }
            
            .appointments-table {
                font-size: 12px;
            }
            
            .appointments-table th,
            .appointments-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="../../dashboard.php" class="home-btn">
        <span style="font-size: 20px;">🏠</span>
        <span>Trang chủ</span>
    </a>
    
    <div class="module-container fade-in">
        <div class="module-header">
            <h1>
                <span style="font-size: 40px;">📅</span>
                Quản lý Lịch hẹn Nha khoa
            </h1>
            <a href="add.php" class="btn btn-primary">+ Đặt lịch hẹn mới</a>
        </div>
        
        <!-- Thống kê nhanh -->
        <div class="stats-row">
            <?php
            $today_result = $database->fetchOne("SELECT COUNT(*) as count FROM appointments WHERE appointment_date = CURDATE()");
            $today_count = $today_result ? $today_result['count'] : 0;

            $pending_result = $database->fetchOne("SELECT COUNT(*) as count FROM appointments WHERE status IN ('dat_lich', 'xac_nhan')");
            $pending_count = $pending_result ? $pending_result['count'] : 0;

            $completed_result = $database->fetchOne("SELECT COUNT(*) as count FROM appointments WHERE appointment_date = CURDATE() AND status = 'hoan_thanh'");
            $completed_today = $completed_result ? $completed_result['count'] : 0;
            ?>
            <div class="stat-card">
                <div class="stat-number"><?php echo $today_count; ?></div>
                <div class="stat-label">Lịch hẹn hôm nay</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $pending_count; ?></div>
                <div class="stat-label">Chờ khám</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $completed_today; ?></div>
                <div class="stat-label">Đã khám hôm nay</div>
            </div>
        </div>
        
        <!-- Thông báo -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <!-- Bộ lọc -->
        <form method="GET" class="filter-box">
            <div class="form-group">
                <label>Tìm kiếm</label>
                <input type="text" name="search" placeholder="Tên bệnh nhân, số điện thoại..."
                       value="<?php echo htmlspecialchars($search); ?>">
            </div>
            
            <div class="form-group">
                <label>Trạng thái</label>
                <select name="status">
                    <option value="">-- Tất cả --</option>
                    <option value="dat_lich" <?php echo ($status == 'dat_lich') ? 'selected' : ''; ?>>📅 Đã đặt</option>
                    <option value="xac_nhan" <?php echo ($status == 'xac_nhan') ? 'selected' : ''; ?>>✅ Xác nhận</option>
                    <option value="dang_kham" <?php echo ($status == 'dang_kham') ? 'selected' : ''; ?>>🔄 Đang khám</option>
                    <option value="hoan_thanh" <?php echo ($status == 'hoan_thanh') ? 'selected' : ''; ?>>✅ Hoàn thành</option>
                    <option value="huy_lich" <?php echo ($status == 'huy_lich') ? 'selected' : ''; ?>>❌ Đã hủy</option>
                    <option value="khong_den" <?php echo ($status == 'khong_den') ? 'selected' : ''; ?>>👻 Không đến</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>Từ ngày</label>
                <input type="date" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
            </div>
            
            <div class="form-group">
                <label>Đến ngày</label>
                <input type="date" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
            </div>
            
            <!-- Loại bỏ dropdown bác sĩ vì không có bảng dentists -->
            
            <button type="submit" class="btn btn-primary">🔍 Lọc</button>
        </form>
        
        <!-- Danh sách lịch hẹn -->
        <?php if (empty($appointments)): ?>
            <div style="text-align: center; padding: 60px; color: #666; background: #f8f9fa; border-radius: 15px;">
                <span style="font-size: 60px;">📅</span>
                <h3>Không có lịch hẹn nào</h3>
                <p>Thử thay đổi bộ lọc hoặc tạo lịch hẹn mới</p>
            </div>
        <?php else: ?>
            <table class="appointments-table">
                <thead>
                    <tr>
                        <th>Mã lịch hẹn</th>
                        <th>Bệnh nhân</th>
                        <th>Dịch vụ</th>
                        <th>Ngày khám</th>
                        <th>Giờ khám</th>
                        <th>Trạng thái</th>
                        <th>Ưu tiên</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($appointments as $appointment): ?>
                        <tr>
                            <td>
                                <strong>LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></strong>
                            </td>
                            <td>
                                <div class="patient-name"><?php echo htmlspecialchars($appointment['patient_name']); ?></div>
                                <small>📞 <?php echo htmlspecialchars($appointment['patient_phone']); ?></small>
                            </td>
                            <td><?php echo getServiceTypeLabel($appointment['service_type']); ?></td>
                            <td>
                                <strong><?php echo $appointment['date_display']; ?></strong>
                            </td>
                            <td>
                                <strong><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></strong>
                                <br><small><?php echo $appointment['duration']; ?> phút</small>
                            </td>
                            <td><?php echo getStatusBadge($appointment['status']); ?></td>
                            <td><?php echo getPriorityBadge($appointment['priority']); ?></td>
                            <td>
                                <a href="view.php?id=<?php echo $appointment['id']; ?>"
                                   class="btn btn-secondary" style="padding: 6px 10px; font-size: 12px;">👁️</a>
                                <a href="edit.php?id=<?php echo $appointment['id']; ?>"
                                   class="btn btn-warning" style="padding: 6px 10px; font-size: 12px;">✏️</a>
                                <?php if ($appointment['status'] != 'hoan_thanh' && $appointment['status'] != 'huy_lich'): ?>
                                    <a href="cancel.php?id=<?php echo $appointment['id']; ?>"
                                       class="btn btn-danger" style="padding: 6px 10px; font-size: 12px;"
                                       onclick="return confirm('Bạn có chắc muốn hủy lịch hẹn này?')">❌</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 20px; color: #666;">
            Tổng cộng: <?php echo $total_records; ?> lịch hẹn
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.module-container').classList.add('fade-in');
        });
    </script>
</body>
</html>
