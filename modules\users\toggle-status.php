<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

// Ki<PERSON><PERSON> tra quyền admin
if ($current_user['role'] !== 'dentist') {
    header("Location: ../../dashboard.php?error=access_denied");
    exit();
}

$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$user_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

// Không cho phép khóa chính mình
if ($user_id == $current_user['id']) {
    header("Location: list.php?error=cannot_lock_self");
    exit();
}

// Lấy thông tin user
$sql = "SELECT id, username, full_name, status FROM users WHERE id = ?";
$user = $database->fetchOne($sql, [$user_id]);

if (!$user) {
    header("Location: list.php?error=user_not_found");
    exit();
}

try {
    // Toggle status
    $new_status = ($user['status'] == 'active') ? 'inactive' : 'active';
    
    $update_sql = "UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?";
    
    if ($database->execute($update_sql, [$new_status, $user_id])) {
        $action = ($new_status == 'active') ? 'kích hoạt' : 'khóa';
        $message = "Đã {$action} tài khoản {$user['full_name']} thành công!";
        header("Location: list.php?success=" . urlencode($message));
    } else {
        header("Location: list.php?error=update_failed");
    }
} catch(Exception $e) {
    header("Location: list.php?error=" . urlencode("Lỗi hệ thống: " . $e->getMessage()));
}
exit();
?>
