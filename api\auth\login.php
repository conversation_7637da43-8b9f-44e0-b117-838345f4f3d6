<?php
/**
 * API Login Endpoint
 * POST /api/auth/login
 */

require_once '../config/cors.php';
require_once '../config/response.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';

// Only allow POST method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ApiResponse::error('Method not allowed', 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        ApiResponse::error('Invalid JSON input', 400);
    }
    
    // Validate required fields
    ApiResponse::validateRequired($input, ['username', 'password']);
    
    // Sanitize input
    $username = ApiResponse::sanitize($input['username']);
    $password = $input['password']; // Don't sanitize password
    
    // Authenticate user
    $auth = new Auth();
    $result = $auth->login($username, $password);
    
    if ($result['success']) {
        // Generate simple token (in production, use JWT)
        $token = base64_encode($username . ':' . time() . ':' . uniqid());
        
        // Store token in session for API authentication
        session_start();
        $_SESSION['api_token'] = $token;
        $_SESSION['api_user_id'] = $result['user']['id'];
        
        // Return success response
        ApiResponse::success([
            'token' => $token,
            'user' => [
                'id' => $result['user']['id'],
                'username' => $result['user']['username'],
                'full_name' => $result['user']['full_name'],
                'role' => $result['user']['role'],
                'email' => $result['user']['email'],
                'clinic_name' => $result['user']['clinic_name']
            ]
        ], 'Đăng nhập thành công');
        
    } else {
        ApiResponse::error($result['message'], 401);
    }
    
} catch (Exception $e) {
    error_log("API Login Error: " . $e->getMessage());
    ApiResponse::error('Lỗi hệ thống', 500);
}
?>
