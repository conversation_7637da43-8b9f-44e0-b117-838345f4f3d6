<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

// Lấy tham số thời gian
$period = isset($_GET['period']) ? $_GET['period'] : 'month';
$custom_from = isset($_GET['from']) ? $_GET['from'] : '';
$custom_to = isset($_GET['to']) ? $_GET['to'] : '';

// <PERSON><PERSON><PERSON> đ<PERSON>nh kho<PERSON>ng thời gian
switch ($period) {
    case 'today':
        $date_from = date('Y-m-d');
        $date_to = date('Y-m-d');
        break;
    case 'week':
        $date_from = date('Y-m-d', strtotime('monday this week'));
        $date_to = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'month':
        $date_from = date('Y-m-01');
        $date_to = date('Y-m-t');
        break;
    case 'year':
        $date_from = date('Y-01-01');
        $date_to = date('Y-12-31');
        break;
    case 'custom':
        $date_from = $custom_from ?: date('Y-m-01');
        $date_to = $custom_to ?: date('Y-m-t');
        break;
    default:
        $date_from = date('Y-m-01');
        $date_to = date('Y-m-t');
}

// Thống kê tổng quan
$stats = [];

// 1. Tổng số bệnh nhân
$stats['total_patients'] = $database->fetchOne("SELECT COUNT(*) as count FROM benhnhan")['count'];

// 2. Bệnh nhân mới trong kỳ
$stats['new_patients'] = $database->fetchOne(
    "SELECT COUNT(*) as count FROM benhnhan WHERE DATE(created_at) BETWEEN ? AND ?", 
    [$date_from, $date_to]
)['count'];

// 3. Tổng lịch hẹn trong kỳ
$stats['total_appointments'] = $database->fetchOne(
    "SELECT COUNT(*) as count FROM appointments WHERE appointment_date BETWEEN ? AND ?", 
    [$date_from, $date_to]
)['count'];

// 4. Lịch hẹn hoàn thành
$stats['completed_appointments'] = $database->fetchOne(
    "SELECT COUNT(*) as count FROM appointments WHERE appointment_date BETWEEN ? AND ? AND status = 'hoan_thanh'", 
    [$date_from, $date_to]
)['count'];

// 5. Lịch hẹn bị hủy
$stats['cancelled_appointments'] = $database->fetchOne(
    "SELECT COUNT(*) as count FROM appointments WHERE appointment_date BETWEEN ? AND ? AND status IN ('huy_lich', 'khong_den')", 
    [$date_from, $date_to]
)['count'];

// 6. Doanh thu
$revenue = $database->fetchOne(
    "SELECT SUM(actual_cost) as total FROM appointments WHERE appointment_date BETWEEN ? AND ? AND payment_status = 'da_thanh_toan'", 
    [$date_from, $date_to]
);
$stats['revenue'] = $revenue['total'] ?: 0;

// 7. Doanh thu dự kiến
$estimated_revenue = $database->fetchOne(
    "SELECT SUM(estimated_cost) as total FROM appointments WHERE appointment_date BETWEEN ? AND ? AND status = 'hoan_thanh'", 
    [$date_from, $date_to]
);
$stats['estimated_revenue'] = $estimated_revenue['total'] ?: 0;

// Thống kê theo dịch vụ
$service_stats = $database->fetchAll(
    "SELECT service_type, COUNT(*) as count, SUM(estimated_cost) as revenue 
     FROM appointments 
     WHERE appointment_date BETWEEN ? AND ? AND status = 'hoan_thanh'
     GROUP BY service_type 
     ORDER BY count DESC", 
    [$date_from, $date_to]
);

// Thống kê theo ngày (7 ngày gần nhất)
$daily_stats = $database->fetchAll(
    "SELECT appointment_date, 
            COUNT(*) as appointments,
            SUM(CASE WHEN status = 'hoan_thanh' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN status = 'hoan_thanh' THEN estimated_cost ELSE 0 END) as revenue
     FROM appointments 
     WHERE appointment_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 6 DAY) AND CURDATE()
     GROUP BY appointment_date 
     ORDER BY appointment_date DESC"
);

// Tỷ lệ hoàn thành
$completion_rate = $stats['total_appointments'] > 0 ? 
    round(($stats['completed_appointments'] / $stats['total_appointments']) * 100, 1) : 0;

// Tỷ lệ hủy
$cancellation_rate = $stats['total_appointments'] > 0 ? 
    round(($stats['cancelled_appointments'] / $stats['total_appointments']) * 100, 1) : 0;
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Báo cáo & Thống kê</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/navigation-buttons.css">
    <style>
        .reports-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .period-selector {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .period-btn {
            padding: 8px 16px;
            border: 2px solid #ddd;
            border-radius: 20px;
            background: white;
            text-decoration: none;
            color: #666;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .period-btn.active {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card.revenue {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
        
        .stat-card.patients {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        
        .stat-card.appointments {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        
        .service-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .service-item:last-child {
            border-bottom: none;
        }
        
        .service-bar {
            height: 8px;
            background: #4CAF50;
            border-radius: 4px;
            margin-top: 5px;
        }
        
        .daily-chart {
            display: flex;
            align-items: end;
            gap: 10px;
            height: 200px;
            padding: 20px 0;
        }
        
        .daily-bar {
            flex: 1;
            background: #4CAF50;
            border-radius: 4px 4px 0 0;
            min-height: 20px;
            position: relative;
            display: flex;
            align-items: end;
            justify-content: center;
        }
        
        .daily-label {
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            color: #666;
            transform: rotate(-45deg);
            transform-origin: center;
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .period-selector {
                flex-direction: column;
                align-items: stretch;
            }
            
            .stats-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <?php
    require_once '../../includes/navigation-buttons.php';
    renderNavigationButtons();
    ?>

    <div class="reports-container fade-in">
        <div class="module-header">
            <h1>
                <span style="font-size: 40px;">📊</span>
                Báo cáo & Thống kê
            </h1>
            <p style="color: #666;">
                Kỳ báo cáo: <strong><?php echo date('d/m/Y', strtotime($date_from)); ?></strong> 
                đến <strong><?php echo date('d/m/Y', strtotime($date_to)); ?></strong>
            </p>
        </div>

        <!-- Chọn kỳ báo cáo -->
        <div class="period-selector">
            <strong>Chọn kỳ:</strong>
            <a href="?period=today" class="period-btn <?php echo $period == 'today' ? 'active' : ''; ?>">Hôm nay</a>
            <a href="?period=week" class="period-btn <?php echo $period == 'week' ? 'active' : ''; ?>">Tuần này</a>
            <a href="?period=month" class="period-btn <?php echo $period == 'month' ? 'active' : ''; ?>">Tháng này</a>
            <a href="?period=year" class="period-btn <?php echo $period == 'year' ? 'active' : ''; ?>">Năm này</a>
            
            <form method="GET" style="display: flex; gap: 10px; align-items: center;">
                <input type="hidden" name="period" value="custom">
                <input type="date" name="from" value="<?php echo $custom_from; ?>" style="padding: 5px; border-radius: 5px; border: 1px solid #ddd;">
                <span>đến</span>
                <input type="date" name="to" value="<?php echo $custom_to; ?>" style="padding: 5px; border-radius: 5px; border: 1px solid #ddd;">
                <button type="submit" class="btn btn-primary" style="padding: 5px 15px;">Xem</button>
            </form>
        </div>

        <!-- Thống kê tổng quan -->
        <div class="stats-grid">
            <div class="stat-card revenue">
                <div class="stat-number"><?php echo number_format($stats['revenue'], 0, ',', '.'); ?>đ</div>
                <div class="stat-label">💰 Doanh thu thực tế</div>
            </div>
            
            <div class="stat-card patients">
                <div class="stat-number"><?php echo $stats['total_patients']; ?></div>
                <div class="stat-label">👥 Tổng bệnh nhân</div>
            </div>
            
            <div class="stat-card patients">
                <div class="stat-number"><?php echo $stats['new_patients']; ?></div>
                <div class="stat-label">🆕 Bệnh nhân mới</div>
            </div>
            
            <div class="stat-card appointments">
                <div class="stat-number"><?php echo $stats['total_appointments']; ?></div>
                <div class="stat-label">📅 Tổng lịch hẹn</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['completed_appointments']; ?></div>
                <div class="stat-label">✅ Đã hoàn thành</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number"><?php echo $completion_rate; ?>%</div>
                <div class="stat-label">📈 Tỷ lệ hoàn thành</div>
            </div>
        </div>

        <!-- Biểu đồ -->
        <div class="charts-grid">
            <!-- Thống kê theo dịch vụ -->
            <div class="chart-card">
                <div class="chart-title">🦷 Dịch vụ phổ biến</div>
                <?php if (!empty($service_stats)): ?>
                    <?php 
                    $max_count = max(array_column($service_stats, 'count'));
                    $service_labels = [
                        'kham_tong_quat' => '🔍 Khám tổng quát',
                        'tay_trang' => '✨ Tẩy trắng',
                        'nho_rang' => '🦷 Nhổ răng',
                        'han_tram' => '🔧 Hàn trám',
                        'dieu_tri_tuy' => '🩺 Điều trị tủy',
                        'nieng_rang' => '📐 Niềng răng',
                        'cay_ghep' => '🔩 Cấy ghép',
                        'phau_thuat' => '⚕️ Phẫu thuật',
                        'tu_van' => '💬 Tư vấn'
                    ];
                    ?>
                    <?php foreach ($service_stats as $service): ?>
                        <div class="service-item">
                            <div>
                                <strong><?php echo $service_labels[$service['service_type']] ?? $service['service_type']; ?></strong>
                                <div style="font-size: 12px; color: #666;">
                                    <?php echo $service['count']; ?> lượt - <?php echo number_format($service['revenue'], 0, ',', '.'); ?>đ
                                </div>
                            </div>
                            <div style="width: 100px;">
                                <div class="service-bar" style="width: <?php echo ($service['count'] / $max_count) * 100; ?>%;"></div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p style="text-align: center; color: #666;">Chưa có dữ liệu trong kỳ này</p>
                <?php endif; ?>
            </div>

            <!-- Thống kê theo ngày -->
            <div class="chart-card">
                <div class="chart-title">📈 Lịch hẹn 7 ngày gần nhất</div>
                <?php if (!empty($daily_stats)): ?>
                    <div class="daily-chart">
                        <?php 
                        $max_appointments = max(array_column($daily_stats, 'appointments'));
                        foreach (array_reverse($daily_stats) as $day): 
                        ?>
                            <div class="daily-bar" 
                                 style="height: <?php echo $max_appointments > 0 ? ($day['appointments'] / $max_appointments) * 150 + 20 : 20; ?>px;"
                                 title="<?php echo date('d/m', strtotime($day['appointment_date'])); ?>: <?php echo $day['appointments']; ?> lịch hẹn">
                                <div class="daily-label"><?php echo date('d/m', strtotime($day['appointment_date'])); ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p style="text-align: center; color: #666;">Chưa có dữ liệu</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Thông tin bổ sung -->
        <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin-top: 20px;">
            <h3>📋 Tóm tắt hiệu suất</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>Tỷ lệ hoàn thành:</strong> 
                    <span style="color: <?php echo $completion_rate >= 80 ? '#4CAF50' : ($completion_rate >= 60 ? '#FF9800' : '#f44336'); ?>;">
                        <?php echo $completion_rate; ?>%
                    </span>
                </div>
                <div>
                    <strong>Tỷ lệ hủy lịch:</strong> 
                    <span style="color: <?php echo $cancellation_rate <= 10 ? '#4CAF50' : ($cancellation_rate <= 20 ? '#FF9800' : '#f44336'); ?>;">
                        <?php echo $cancellation_rate; ?>%
                    </span>
                </div>
                <div>
                    <strong>Doanh thu trung bình/ngày:</strong> 
                    <?php 
                    $days = (strtotime($date_to) - strtotime($date_from)) / (60*60*24) + 1;
                    echo number_format($stats['revenue'] / $days, 0, ',', '.'); 
                    ?>đ
                </div>
                <div>
                    <strong>Lịch hẹn trung bình/ngày:</strong> 
                    <?php echo round($stats['total_appointments'] / $days, 1); ?> lịch hẹn
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.reports-container').classList.add('fade-in');
        });
    </script>
</body>
</html>
