<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $patient_id = (int)$_POST['patient_id'];
    $service_type = $_POST['service_type'] ?? 'kham_tong_quat';
    $appointment_date = $_POST['appointment_date'];
    $appointment_time = $_POST['appointment_time'];
    $duration = (int)($_POST['duration'] ?? 30);
    $reason = trim($_POST['reason']);
    $priority = $_POST['priority'] ?? 'binh_thuong';
    $notes = trim($_POST['notes']);
    $estimated_cost = !empty($_POST['estimated_cost']) ? (float)$_POST['estimated_cost'] : null;

    // Validation
    if ($patient_id <= 0) {
        $error = "Vui lòng chọn bệnh nhân!";
    } elseif (empty($appointment_date)) {
        $error = "Vui lòng chọn ngày khám!";
    } elseif (empty($appointment_time)) {
        $error = "Vui lòng chọn giờ khám!";
    } elseif (strtotime($appointment_date) < strtotime(date('Y-m-d'))) {
        $error = "Ngày khám không được trong quá khứ!";
    } elseif (empty($reason)) {
        $error = "Vui lòng nhập lý do khám!";
    } else {
        try {
            // Kiểm tra bệnh nhân có tồn tại không
            $patient_check = $database->fetchOne("SELECT id FROM benhnhan WHERE id = ?", [$patient_id]);
            if (!$patient_check) {
                $error = "Bệnh nhân được chọn không hợp lệ!";
            } else {
                // Kiểm tra trùng lịch (cùng ngày, cùng giờ)
                $check_sql = "SELECT id FROM appointments
                             WHERE appointment_date = ? AND appointment_time = ?
                             AND status NOT IN ('huy_lich', 'khong_den')";
                $existing = $database->fetchOne($check_sql, [$appointment_date, $appointment_time]);

                if ($existing) {
                    $error = "Đã có lịch hẹn vào thời gian này! Vui lòng chọn giờ khác.";
                } else {
                    // Thêm lịch hẹn mới
                    $sql = "INSERT INTO appointments (patient_id, appointment_date, appointment_time, duration, reason, service_type, priority, status, notes, estimated_cost, created_by, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, 'dat_lich', ?, ?, ?, NOW())";

                    $params = [
                        $patient_id,
                        $appointment_date,
                        $appointment_time,
                        $duration,
                        $reason,
                        $service_type,
                        $priority,
                        $notes,
                        $estimated_cost,
                        $current_user['id']
                    ];

                    if ($database->execute($sql, $params)) {
                        $success = "Đặt lịch hẹn thành công!";
                        // Redirect sau 2 giây
                        header("refresh:2;url=list.php");
                    } else {
                        $error = "Lỗi khi đặt lịch hẹn! Vui lòng thử lại.";

                        // Log lỗi để debug
                        error_log("Appointment creation failed");
                        error_log("SQL: " . $sql);
                        error_log("Params: " . print_r($params, true));
                    }
                }
            }
        } catch(Exception $e) {
            $error = "Lỗi hệ thống: " . $e->getMessage();
            error_log("Appointment creation error: " . $e->getMessage());
        }
    }
}

// Lấy danh sách bệnh nhân từ bảng benhnhan
$patients = $database->fetchAll("SELECT id, hoten as full_name, sdt as phone, email FROM benhnhan ORDER BY hoten");

// Kiểm tra dữ liệu cần thiết
if (empty($patients)) {
    $error = "Chưa có bệnh nhân nào trong hệ thống. Vui lòng thêm bệnh nhân trước khi đặt lịch hẹn.";
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đặt lịch hẹn mới - Phòng khám Nha khoa</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .form-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px auto;
            max-width: 900px;
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .home-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
            background: linear-gradient(45deg, #ee5a52, #ff6b6b);
        }
        
        .service-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 10px;
            display: none;
        }
        
        .priority-urgent {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .priority-emergency {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="../../dashboard.php" class="home-btn">
        <span style="font-size: 20px;">🏠</span>
        <span>Trang chủ</span>
    </a>
    
    <div class="form-container fade-in">
        <div class="form-header">
            <h1>
                <span style="font-size: 40px;">📅</span>
                Đặt lịch hẹn mới
            </h1>
            <p style="color: #666; margin-top: 10px;">
                Tạo lịch hẹn khám cho bệnh nhân
            </p>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <form method="POST" id="appointmentForm">
            <div class="form-grid">
                <div class="form-group">
                    <label for="patient_id">Bệnh nhân *</label>
                    <select id="patient_id" name="patient_id" required>
                        <option value="">-- Chọn bệnh nhân --</option>
                        <?php foreach ($patients as $patient): ?>
                            <option value="<?php echo $patient['id']; ?>"
                                    data-phone="<?php echo htmlspecialchars($patient['phone'] ?? ''); ?>"
                                    data-email="<?php echo htmlspecialchars($patient['email'] ?? ''); ?>"
                                    <?php echo (($_POST['patient_id'] ?? '') == $patient['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($patient['full_name']); ?>
                                <?php if ($patient['phone']): ?>
                                    - <?php echo htmlspecialchars($patient['phone']); ?>
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div id="patient-info" style="margin-top: 5px; font-size: 14px; color: #666;"></div>
                </div>
                
                <div class="form-group">
                    <label for="service_type">Loại dịch vụ *</label>
                    <select id="service_type" name="service_type" required>
                        <option value="kham_tong_quat" <?php echo (($_POST['service_type'] ?? 'kham_tong_quat') == 'kham_tong_quat') ? 'selected' : ''; ?>>🔍 Khám tổng quát</option>
                        <option value="tay_trang" <?php echo (($_POST['service_type'] ?? '') == 'tay_trang') ? 'selected' : ''; ?>>✨ Tẩy trắng răng</option>
                        <option value="nho_rang" <?php echo (($_POST['service_type'] ?? '') == 'nho_rang') ? 'selected' : ''; ?>>🦷 Nhổ răng</option>
                        <option value="han_tram" <?php echo (($_POST['service_type'] ?? '') == 'han_tram') ? 'selected' : ''; ?>>🔧 Hàn trám</option>
                        <option value="dieu_tri_tuy" <?php echo (($_POST['service_type'] ?? '') == 'dieu_tri_tuy') ? 'selected' : ''; ?>>🩺 Điều trị tủy</option>
                        <option value="nieng_rang" <?php echo (($_POST['service_type'] ?? '') == 'nieng_rang') ? 'selected' : ''; ?>>📐 Niềng răng</option>
                        <option value="cay_ghep" <?php echo (($_POST['service_type'] ?? '') == 'cay_ghep') ? 'selected' : ''; ?>>🔩 Cấy ghép implant</option>
                        <option value="phau_thuat" <?php echo (($_POST['service_type'] ?? '') == 'phau_thuat') ? 'selected' : ''; ?>>⚕️ Phẫu thuật</option>
                        <option value="tu_van" <?php echo (($_POST['service_type'] ?? '') == 'tu_van') ? 'selected' : ''; ?>>💬 Tư vấn</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="duration">Thời gian dự kiến (phút)</label>
                    <input type="number" id="duration" name="duration" min="15" max="180" step="15"
                           value="<?php echo htmlspecialchars($_POST['duration'] ?? '30'); ?>" placeholder="30">
                </div>
                
                <div class="form-group">
                    <label for="priority">Mức độ ưu tiên</label>
                    <select id="priority" name="priority">
                        <option value="binh_thuong" <?php echo (($_POST['priority'] ?? 'binh_thuong') == 'binh_thuong') ? 'selected' : ''; ?>>🟢 Bình thường</option>
                        <option value="khan_cap" <?php echo (($_POST['priority'] ?? '') == 'khan_cap') ? 'selected' : ''; ?>>🟡 Khẩn cấp</option>
                        <option value="uu_tien" <?php echo (($_POST['priority'] ?? '') == 'uu_tien') ? 'selected' : ''; ?>>🔴 Ưu tiên</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="estimated_cost">Chi phí dự kiến (VNĐ)</label>
                    <input type="number" id="estimated_cost" name="estimated_cost" min="0" step="1000"
                           value="<?php echo htmlspecialchars($_POST['estimated_cost'] ?? ''); ?>" placeholder="200000">
                </div>
                
                <div class="form-group">
                    <label for="appointment_date">Ngày khám *</label>
                    <input type="date" id="appointment_date" name="appointment_date" required 
                           min="<?php echo date('Y-m-d'); ?>"
                           value="<?php echo htmlspecialchars($_POST['appointment_date'] ?? ''); ?>">
                </div>
                
                <div class="form-group">
                    <label for="appointment_time">Giờ khám *</label>
                    <input type="time" id="appointment_time" name="appointment_time" required 
                           min="08:00" max="17:00"
                           value="<?php echo htmlspecialchars($_POST['appointment_time'] ?? ''); ?>">
                </div>
                
                <div class="form-group full-width">
                    <label for="reason">Lý do khám</label>
                    <textarea id="reason" name="reason" 
                              placeholder="Mô tả triệu chứng, lý do đến khám..."><?php echo htmlspecialchars($_POST['reason'] ?? ''); ?></textarea>
                </div>
                
                <div class="form-group full-width">
                    <label for="notes">Ghi chú</label>
                    <textarea id="notes" name="notes" 
                              placeholder="Ghi chú thêm cho lịch hẹn..."><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn btn-primary">
                    📅 Đặt lịch hẹn
                </button>
                <a href="list.php" class="btn btn-secondary">❌ Hủy</a>
            </div>
        </form>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.form-container').classList.add('fade-in');
            
            // Show patient info
            const patientSelect = document.getElementById('patient_id');
            const patientInfo = document.getElementById('patient-info');

            patientSelect.addEventListener('change', function() {
                const option = this.options[this.selectedIndex];
                if (option.value) {
                    const phone = option.dataset.phone;
                    const email = option.dataset.email;
                    let info = '';
                    if (phone) info += `📞 ${phone}`;
                    if (email) info += (info ? ' | ' : '') + `📧 ${email}`;
                    patientInfo.innerHTML = info;
                } else {
                    patientInfo.innerHTML = '';
                }
            });

            // Auto-update duration based on service type
            const serviceTypeSelect = document.getElementById('service_type');
            const durationInput = document.getElementById('duration');
            const estimatedCostInput = document.getElementById('estimated_cost');

            serviceTypeSelect.addEventListener('change', function() {
                const serviceDurations = {
                    'kham_tong_quat': { duration: 30, cost: 200000 },
                    'tay_trang': { duration: 90, cost: 1500000 },
                    'nho_rang': { duration: 30, cost: 300000 },
                    'han_tram': { duration: 45, cost: 500000 },
                    'dieu_tri_tuy': { duration: 60, cost: 800000 },
                    'nieng_rang': { duration: 60, cost: 2000000 },
                    'cay_ghep': { duration: 120, cost: 15000000 },
                    'phau_thuat': { duration: 90, cost: 3000000 },
                    'tu_van': { duration: 30, cost: 100000 }
                };

                const selected = serviceDurations[this.value];
                if (selected) {
                    durationInput.value = selected.duration;
                    if (!estimatedCostInput.value) {
                        estimatedCostInput.value = selected.cost;
                    }
                }
            });
            
            // Priority styling
            const prioritySelect = document.getElementById('priority');
            prioritySelect.addEventListener('change', function() {
                const form = document.getElementById('appointmentForm');
                form.classList.remove('priority-urgent', 'priority-emergency');

                if (this.value === 'khan_cap') {
                    form.classList.add('priority-urgent');
                } else if (this.value === 'uu_tien') {
                    form.classList.add('priority-emergency');
                }
            });

            // Set default time
            const timeInput = document.getElementById('appointment_time');
            if (!timeInput.value) {
                timeInput.value = '09:00';
            }

            // Form validation
            const form = document.getElementById('appointmentForm');
            form.addEventListener('submit', function(e) {
                let isValid = true;
                let errorMessage = '';

                // Kiểm tra bệnh nhân
                if (!patientSelect.value) {
                    isValid = false;
                    errorMessage += '• Vui lòng chọn bệnh nhân\n';
                }

                // Kiểm tra loại dịch vụ
                if (!serviceTypeSelect.value) {
                    isValid = false;
                    errorMessage += '• Vui lòng chọn loại dịch vụ\n';
                }

                // Kiểm tra ngày
                const dateInput = document.getElementById('appointment_date');
                if (!dateInput.value) {
                    isValid = false;
                    errorMessage += '• Vui lòng chọn ngày khám\n';
                } else {
                    const selectedDate = new Date(dateInput.value);
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    if (selectedDate < today) {
                        isValid = false;
                        errorMessage += '• Ngày khám không thể là ngày trong quá khứ\n';
                    }
                }

                // Kiểm tra giờ
                if (!timeInput.value) {
                    isValid = false;
                    errorMessage += '• Vui lòng chọn giờ khám\n';
                } else {
                    const time = timeInput.value;
                    if (time < '08:00' || time > '17:00') {
                        isValid = false;
                        errorMessage += '• Giờ khám phải từ 8:00 đến 17:00\n';
                    }
                }

                // Kiểm tra lý do khám
                const reasonInput = document.getElementById('reason');
                if (!reasonInput.value.trim()) {
                    isValid = false;
                    errorMessage += '• Vui lòng nhập lý do khám\n';
                }

                if (!isValid) {
                    e.preventDefault();
                    alert('Vui lòng kiểm tra lại thông tin:\n\n' + errorMessage);
                    return false;
                }

                // Hiển thị loading
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '⏳ Đang xử lý...';

                return true;
            });

            // Trigger initial events
            patientSelect.dispatchEvent(new Event('change'));
            serviceTypeSelect.dispatchEvent(new Event('change'));
        });
    </script>
</body>
</html>
