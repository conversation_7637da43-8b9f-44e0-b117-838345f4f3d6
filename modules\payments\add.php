<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';
require_once '../../includes/navigation-buttons.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

// Lấy danh sách lịch hẹn chưa thanh toán hoặc thanh toán một phần
$sql = "SELECT a.id, a.appointment_date, a.appointment_time, a.service_type,
               a.estimated_cost, a.actual_cost, a.payment_status, a.status,
               b.hoten as patient_name, b.sdt as patient_phone
        FROM appointments a 
        LEFT JOIN benhnhan b ON a.patient_id = b.id 
        WHERE a.payment_status IN ('chua_thanh_toan', 'thanh_toan_mot_phan')
        AND a.status = 'hoan_thanh'
        ORDER BY a.appointment_date DESC, a.appointment_time DESC";

$appointments = $database->fetchAll($sql);

function getServiceTypeLabel($service_type) {
    $labels = [
        'kham_tong_quat' => '🔍 Khám tổng quát',
        'tay_trang' => '✨ Tẩy trắng',
        'nho_rang' => '🦷 Nhổ răng',
        'han_tram' => '🔧 Hàn trám',
        'dieu_tri_tuy' => '🩺 Điều trị tủy',
        'nieng_rang' => '📐 Niềng răng',
        'cay_ghep' => '🔩 Cấy ghép',
        'phau_thuat' => '⚕️ Phẫu thuật',
        'tu_van' => '💬 Tư vấn'
    ];
    return $labels[$service_type] ?? '🔍 Khám tổng quát';
}

function getPaymentStatusBadge($status) {
    $badges = [
        'chua_thanh_toan' => '<span style="background: #ff9800; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">💳 Chưa thanh toán</span>',
        'thanh_toan_mot_phan' => '<span style="background: #2196f3; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">📊 Thanh toán một phần</span>'
    ];
    return $badges[$status] ?? $status;
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💳 Ghi nhận Thanh toán</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/navigation-buttons.css">
    <style>
        .add-payment-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .appointments-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .appointments-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .appointments-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }
        
        .appointments-table tr:hover {
            background: #f8f9fa;
        }
        
        .amount {
            font-weight: bold;
            font-size: 16px;
        }
        
        .amount.estimated {
            color: #666;
        }
        
        .amount.pending {
            color: #FF9800;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-state-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .appointments-table {
                font-size: 14px;
            }
            
            .appointments-table th,
            .appointments-table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <?php renderNavigationButtons('list.php'); ?>

    <div class="add-payment-container fade-in">
        <div class="module-header">
            <h1>
                <span style="font-size: 40px;">💳</span>
                Ghi nhận Thanh toán
            </h1>
            <p style="color: #666; margin-top: 10px;">
                Chọn lịch hẹn cần ghi nhận thanh toán từ danh sách bên dưới
            </p>
        </div>

        <?php if (!empty($appointments)): ?>
            <div style="overflow-x: auto;">
                <table class="appointments-table">
                    <thead>
                        <tr>
                            <th>Mã LH</th>
                            <th>Bệnh nhân</th>
                            <th>Dịch vụ</th>
                            <th>Ngày khám</th>
                            <th>Chi phí dự kiến</th>
                            <th>Đã thanh toán</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($appointments as $appointment): ?>
                            <tr>
                                <td>
                                    <strong>LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></strong>
                                </td>
                                <td>
                                    <div class="patient-name"><?php echo htmlspecialchars($appointment['patient_name']); ?></div>
                                    <small>📞 <?php echo htmlspecialchars($appointment['patient_phone']); ?></small>
                                </td>
                                <td><?php echo getServiceTypeLabel($appointment['service_type']); ?></td>
                                <td>
                                    <strong><?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?></strong>
                                    <br><small><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></small>
                                </td>
                                <td>
                                    <div class="amount estimated">
                                        <?php echo $appointment['estimated_cost'] ? number_format($appointment['estimated_cost'], 0, ',', '.') . 'đ' : 'Chưa xác định'; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="amount <?php echo $appointment['actual_cost'] ? 'actual' : 'pending'; ?>">
                                        <?php echo $appointment['actual_cost'] ? number_format($appointment['actual_cost'], 0, ',', '.') . 'đ' : '0đ'; ?>
                                    </div>
                                </td>
                                <td><?php echo getPaymentStatusBadge($appointment['payment_status']); ?></td>
                                <td>
                                    <a href="process.php?id=<?php echo $appointment['id']; ?>" 
                                       class="btn btn-primary" style="padding: 8px 12px; font-size: 14px;">
                                        💳 Ghi nhận thanh toán
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="empty-state">
                <div class="empty-state-icon">💳</div>
                <h3>Không có lịch hẹn nào cần thanh toán</h3>
                <p>Tất cả lịch hẹn đã hoàn thành đều đã được thanh toán đầy đủ.</p>
                <div style="margin-top: 30px;">
                    <a href="../appointments/list.php" class="btn btn-primary">
                        📅 Xem danh sách lịch hẹn
                    </a>
                    <a href="list.php" class="btn btn-secondary" style="margin-left: 10px;">
                        💰 Quản lý thanh toán
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <div style="text-align: center; margin-top: 30px;">
            <a href="list.php" class="btn btn-secondary" style="padding: 12px 24px;">
                ← Quay lại danh sách thanh toán
            </a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.add-payment-container').classList.add('fade-in');
        });
    </script>
</body>
</html>
