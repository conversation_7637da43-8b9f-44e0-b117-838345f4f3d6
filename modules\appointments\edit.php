<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$appointment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$appointment_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

$database = new Database();

// Lấy thông tin lịch hẹn với database mới
$sql = "SELECT a.*,
               b.hoten as patient_name,
               b.sdt as patient_phone
        FROM appointments a
        LEFT JOIN benhnhan b ON a.patient_id = b.id
        WHERE a.id = ?";

$appointment = $database->fetchOne($sql, [$appointment_id]);

if (!$appointment) {
    header("Location: list.php?error=not_found");
    exit();
}

// <PERSON><PERSON><PERSON> sĩ tư nhân có thể chỉnh sửa tất cả lịch hẹn

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $patient_id = (int)$_POST['patient_id'];
    $service_type = $_POST['service_type'];
    $appointment_date = $_POST['appointment_date'];
    $appointment_time = $_POST['appointment_time'];
    $duration = (int)$_POST['duration'];
    $reason = trim($_POST['reason']);
    $priority = $_POST['priority'];
    $status = $_POST['status'];
    $notes = trim($_POST['notes']);
    $estimated_cost = !empty($_POST['estimated_cost']) ? (int)$_POST['estimated_cost'] : null;
    
    // Validation
    if ($patient_id <= 0) {
        $error = "Vui lòng chọn bệnh nhân!";
    } elseif (empty($service_type)) {
        $error = "Vui lòng chọn loại dịch vụ!";
    } elseif (empty($appointment_date)) {
        $error = "Vui lòng chọn ngày khám!";
    } elseif (empty($appointment_time)) {
        $error = "Vui lòng chọn giờ khám!";
    } elseif ($duration <= 0) {
        $error = "Thời gian khám phải lớn hơn 0!";
    } else {
        try {
            // Kiểm tra trùng lịch (trừ lịch hẹn hiện tại)
            $check_sql = "SELECT id FROM appointments
                         WHERE appointment_date = ? AND appointment_time = ? AND id != ?
                         AND status NOT IN ('huy_lich', 'khong_den')";
            $existing = $database->fetchOne($check_sql, [$appointment_date, $appointment_time, $appointment_id]);

            if ($existing) {
                $error = "Đã có lịch hẹn khác vào thời gian này!";
            } else {
                // Cập nhật lịch hẹn
                $update_sql = "UPDATE appointments SET
                              patient_id = ?, service_type = ?,
                              appointment_date = ?, appointment_time = ?, duration = ?,
                              reason = ?, priority = ?, status = ?, notes = ?, estimated_cost = ?,
                              updated_at = NOW()
                              WHERE id = ?";

                $params = [$patient_id, $service_type, $appointment_date, $appointment_time,
                          $duration, $reason, $priority, $status, $notes, $estimated_cost, $appointment_id];
                
                if ($database->execute($update_sql, $params)) {
                    header("Location: view.php?id=$appointment_id&success=updated");
                    exit();
                } else {
                    $error = "Lỗi khi cập nhật lịch hẹn!";
                }
            }
        } catch(Exception $e) {
            $error = "Lỗi hệ thống: " . $e->getMessage();
        }
    }
}

// Lấy danh sách bệnh nhân từ bảng benhnhan
$patients = $database->fetchAll("SELECT id, hoten as full_name, sdt as phone, email FROM benhnhan ORDER BY hoten");
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chỉnh sửa Lịch hẹn - LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .edit-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px auto;
            max-width: 1000px;
        }
        
        .edit-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .home-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
            background: linear-gradient(45deg, #ee5a52, #ff6b6b);
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1565c0;
        }
        
        .status-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }
        
        .current-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #4CAF50;
        }
        
        .current-info h4 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="../../dashboard.php" class="home-btn">
        <span style="font-size: 20px;">🏠</span>
        <span>Trang chủ</span>
    </a>
    
    <div class="edit-container fade-in">
        <div class="edit-header">
            <h1>
                <span style="font-size: 40px;">✏️</span>
                Chỉnh sửa Lịch hẹn
            </h1>
            <p style="color: #666; margin-top: 10px;">
                Mã lịch hẹn: <strong>LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></strong>
            </p>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <!-- Thông tin hiện tại -->
        <div class="current-info">
            <h4>📋 Thông tin hiện tại</h4>
            <div class="info-item">
                <span class="info-label">Bệnh nhân:</span>
                <span class="info-value"><?php echo htmlspecialchars($appointment['patient_name']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Bác sĩ:</span>
                <span class="info-value">BS. Nguyễn Văn Nam (Bác sĩ tư nhân)</span>
            </div>
            <div class="info-item">
                <span class="info-label">Ngày giờ:</span>
                <span class="info-value">
                    <?php echo date('d/m/Y H:i', strtotime($appointment['appointment_date'] . ' ' . $appointment['appointment_time'])); ?>
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">Trạng thái:</span>
                <span class="info-value">
                    <?php
                    $status_names = [
                        'dat_lich' => '📅 Đã đặt',
                        'xac_nhan' => '✅ Xác nhận',
                        'dang_kham' => '🔄 Đang khám',
                        'hoan_thanh' => '✅ Hoàn thành',
                        'huy_lich' => '❌ Đã hủy',
                        'khong_den' => '👻 Không đến'
                    ];
                    echo $status_names[$appointment['status']] ?? $appointment['status'];
                    ?>
                </span>
            </div>
        </div>
        
        <?php if (in_array($appointment['status'], ['hoan_thanh', 'huy_lich'])): ?>
        <div class="status-warning">
            <strong>⚠️ Cảnh báo:</strong> Lịch hẹn này đã hoàn thành hoặc bị hủy.
            Việc chỉnh sửa có thể ảnh hưởng đến dữ liệu báo cáo.
        </div>
        <?php endif; ?>
        
        <form method="POST" id="editForm">
            <div class="form-grid">
                <div class="form-group">
                    <label for="patient_id">Bệnh nhân *</label>
                    <select id="patient_id" name="patient_id" required>
                        <option value="">-- Chọn bệnh nhân --</option>
                        <?php foreach ($patients as $patient): ?>
                            <option value="<?php echo $patient['id']; ?>"
                                    data-phone="<?php echo htmlspecialchars($patient['phone']); ?>"
                                    <?php echo ($appointment['patient_id'] == $patient['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($patient['full_name']); ?>
                                (<?php echo htmlspecialchars($patient['phone']); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div id="patient-info" style="margin-top: 5px; font-size: 14px; color: #666;"></div>
                </div>
                
                <div class="form-group">
                    <label for="service_type">Loại dịch vụ *</label>
                    <select id="service_type" name="service_type" required>
                        <option value="">-- Chọn loại dịch vụ --</option>
                        <option value="kham_tong_quat" <?php echo ($appointment['service_type'] == 'kham_tong_quat') ? 'selected' : ''; ?>>🔍 Khám tổng quát</option>
                        <option value="tay_trang" <?php echo ($appointment['service_type'] == 'tay_trang') ? 'selected' : ''; ?>>✨ Tẩy trắng</option>
                        <option value="nho_rang" <?php echo ($appointment['service_type'] == 'nho_rang') ? 'selected' : ''; ?>>🦷 Nhổ răng</option>
                        <option value="han_tram" <?php echo ($appointment['service_type'] == 'han_tram') ? 'selected' : ''; ?>>🔧 Hàn trám</option>
                        <option value="dieu_tri_tuy" <?php echo ($appointment['service_type'] == 'dieu_tri_tuy') ? 'selected' : ''; ?>>🩺 Điều trị tủy</option>
                        <option value="nieng_rang" <?php echo ($appointment['service_type'] == 'nieng_rang') ? 'selected' : ''; ?>>📐 Niềng răng</option>
                        <option value="cay_ghep" <?php echo ($appointment['service_type'] == 'cay_ghep') ? 'selected' : ''; ?>>🔩 Cấy ghép</option>
                        <option value="phau_thuat" <?php echo ($appointment['service_type'] == 'phau_thuat') ? 'selected' : ''; ?>>⚕️ Phẫu thuật</option>
                        <option value="tu_van" <?php echo ($appointment['service_type'] == 'tu_van') ? 'selected' : ''; ?>>💬 Tư vấn</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="estimated_cost">Chi phí dự kiến (VNĐ)</label>
                    <input type="number" id="estimated_cost" name="estimated_cost"
                           min="0" step="1000" placeholder="Nhập chi phí dự kiến..."
                           value="<?php echo htmlspecialchars($appointment['estimated_cost'] ?? ''); ?>">
                </div>
                
                <div class="form-group">
                    <label for="status">Trạng thái</label>
                    <select id="status" name="status">
                        <option value="dat_lich" <?php echo ($appointment['status'] == 'dat_lich') ? 'selected' : ''; ?>>📅 Đã đặt</option>
                        <option value="xac_nhan" <?php echo ($appointment['status'] == 'xac_nhan') ? 'selected' : ''; ?>>✅ Xác nhận</option>
                        <option value="dang_kham" <?php echo ($appointment['status'] == 'dang_kham') ? 'selected' : ''; ?>>🔄 Đang khám</option>
                        <option value="hoan_thanh" <?php echo ($appointment['status'] == 'hoan_thanh') ? 'selected' : ''; ?>>✅ Hoàn thành</option>
                        <option value="huy_lich" <?php echo ($appointment['status'] == 'huy_lich') ? 'selected' : ''; ?>>❌ Đã hủy</option>
                        <option value="khong_den" <?php echo ($appointment['status'] == 'khong_den') ? 'selected' : ''; ?>>👻 Không đến</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="appointment_date">Ngày khám *</label>
                    <input type="date" id="appointment_date" name="appointment_date" required 
                           value="<?php echo htmlspecialchars($appointment['appointment_date']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="appointment_time">Giờ khám *</label>
                    <input type="time" id="appointment_time" name="appointment_time" required 
                           value="<?php echo htmlspecialchars($appointment['appointment_time']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="duration">Thời gian (phút) *</label>
                    <input type="number" id="duration" name="duration" required
                           min="15" max="240" step="15"
                           value="<?php echo htmlspecialchars($appointment['duration']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="priority">Mức độ ưu tiên</label>
                    <select id="priority" name="priority">
                        <option value="binh_thuong" <?php echo ($appointment['priority'] == 'binh_thuong') ? 'selected' : ''; ?>>🟢 Bình thường</option>
                        <option value="khan_cap" <?php echo ($appointment['priority'] == 'khan_cap') ? 'selected' : ''; ?>>🟡 Khẩn cấp</option>
                        <option value="uu_tien" <?php echo ($appointment['priority'] == 'uu_tien') ? 'selected' : ''; ?>>🔴 Ưu tiên</option>
                    </select>
                </div>
                
                <div class="form-group full-width">
                    <label for="reason">Lý do khám</label>
                    <textarea id="reason" name="reason" 
                              placeholder="Mô tả triệu chứng, lý do đến khám..."><?php echo htmlspecialchars($appointment['reason']); ?></textarea>
                </div>
                
                <div class="form-group full-width">
                    <label for="notes">Ghi chú</label>
                    <textarea id="notes" name="notes" 
                              placeholder="Ghi chú thêm cho lịch hẹn..."><?php echo htmlspecialchars($appointment['notes']); ?></textarea>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn btn-primary">💾 Cập nhật lịch hẹn</button>
                <a href="view.php?id=<?php echo $appointment_id; ?>" class="btn btn-secondary">❌ Hủy</a>
                <?php if ($appointment['status'] != 'hoan_thanh'): ?>
                    <a href="cancel.php?id=<?php echo $appointment_id; ?>" class="btn btn-warning">🗑️ Hủy lịch hẹn</a>
                <?php endif; ?>
            </div>
        </form>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.edit-container').classList.add('fade-in');
            
            // Show patient info
            const patientSelect = document.getElementById('patient_id');
            const patientInfo = document.getElementById('patient-info');
            
            patientSelect.addEventListener('change', function() {
                const option = this.options[this.selectedIndex];
                if (option.value) {
                    const phone = option.dataset.phone;
                    patientInfo.innerHTML = `📞 ${phone}`;
                } else {
                    patientInfo.innerHTML = '';
                }
            });
            
            // Auto-update duration when service type changes
            const serviceSelect = document.getElementById('service_type');
            const durationInput = document.getElementById('duration');

            serviceSelect.addEventListener('change', function() {
                const durations = {
                    'kham_tong_quat': 30,
                    'tay_trang': 60,
                    'nho_rang': 45,
                    'han_tram': 90,
                    'dieu_tri_tuy': 120,
                    'nieng_rang': 45,
                    'cay_ghep': 60,
                    'phau_thuat': 120,
                    'tu_van': 30
                };

                if (durations[this.value]) {
                    durationInput.value = durations[this.value];
                }
            });
            
            // Trigger initial events
            patientSelect.dispatchEvent(new Event('change'));
            
            // Form validation
            document.getElementById('editForm').addEventListener('submit', function(e) {
                const date = document.getElementById('appointment_date').value;
                const time = document.getElementById('appointment_time').value;
                
                if (date && time) {
                    const appointmentDateTime = new Date(date + 'T' + time);
                    const now = new Date();
                    
                    if (appointmentDateTime < now) {
                        if (!confirm('Thời gian khám đã qua. Bạn có chắc muốn cập nhật?')) {
                            e.preventDefault();
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
