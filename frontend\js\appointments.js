/**
 * Appointments Module
 * Handles appointment management functionality
 */

class AppointmentsModule {
    constructor() {
        this.currentPage = 1;
        this.searchTerm = '';
        this.statusFilter = '';
        this.dateFilter = '';
        this.appointments = [];
        this.totalPages = 1;
    }

    /**
     * Render appointments page
     */
    async render() {
        const content = `
            <div class="fade-in">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-calendar-alt me-2"></i>Quản lý Lịch hẹn</h2>
                    <button class="btn btn-primary" onclick="appointments.showAddModal()">
                        <i class="fas fa-plus me-1"></i>Đặt lịch hẹn
                    </button>
                </div>
                
                <!-- Search and Filters -->
                <div class="search-box">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="input-group">
                                <input type="text" class="form-control" id="appointmentSearch" 
                                       placeholder="T<PERSON><PERSON> kiếm bệnh nhân, lý do...">
                                <button class="btn btn-outline-secondary" onclick="appointments.search()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter" onchange="appointments.filterByStatus()">
                                <option value="">Tất cả trạng thái</option>
                                <option value="dat_lich">Đặt lịch</option>
                                <option value="xac_nhan">Xác nhận</option>
                                <option value="dang_kham">Đang khám</option>
                                <option value="hoan_thanh">Hoàn thành</option>
                                <option value="huy_lich">Hủy lịch</option>
                                <option value="khong_den">Không đến</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="dateFilter" onchange="appointments.filterByDate()">
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-primary w-100" onclick="appointments.clearFilters()">
                                <i class="fas fa-times me-1"></i>Xóa bộ lọc
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Appointments Table -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-list me-2"></i>Danh sách Lịch hẹn
                    </div>
                    <div class="card-body">
                        <div id="appointmentsTableContainer">
                            <div class="text-center py-4">
                                <div class="spinner-border" role="status"></div>
                                <div class="mt-2">Đang tải dữ liệu...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Appointments pagination" class="mt-4">
                    <ul class="pagination" id="appointmentsPagination"></ul>
                </nav>
            </div>
        `;
        
        document.getElementById('pageContent').innerHTML = content;
        
        // Setup search and filters
        this.setupSearch();
        
        // Load appointments data
        await this.loadAppointments();
    }

    /**
     * Setup search functionality
     */
    setupSearch() {
        const searchInput = document.getElementById('appointmentSearch');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.search();
                }
            });
        }
    }

    /**
     * Load appointments data
     */
    async loadAppointments() {
        try {
            const params = {
                page: this.currentPage,
                limit: 10
            };
            
            if (this.searchTerm) params.search = this.searchTerm;
            if (this.statusFilter) params.status = this.statusFilter;
            if (this.dateFilter) params.date_from = this.dateFilter;
            
            const response = await api.getAppointments(params);
            
            if (response.success) {
                this.appointments = response.data;
                this.totalPages = response.pagination.total_pages;
                
                this.renderAppointmentsTable();
                this.renderPagination();
            }
        } catch (error) {
            api.handleError(error);
        }
    }

    /**
     * Render appointments table
     */
    renderAppointmentsTable() {
        const container = document.getElementById('appointmentsTableContainer');
        
        if (this.appointments.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5>Không có lịch hẹn nào</h5>
                    <p class="text-muted">Hãy đặt lịch hẹn đầu tiên</p>
                </div>
            `;
            return;
        }
        
        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Bệnh nhân</th>
                            <th>Ngày & Giờ</th>
                            <th>Dịch vụ</th>
                            <th>Lý do</th>
                            <th>Trạng thái</th>
                            <th>Chi phí</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.appointments.map(appointment => this.renderAppointmentRow(appointment)).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        container.innerHTML = tableHtml;
    }

    /**
     * Render single appointment row
     */
    renderAppointmentRow(appointment) {
        const statusClass = this.getStatusClass(appointment.status);
        
        return `
            <tr>
                <td>
                    <div class="fw-bold">${appointment.patient_name}</div>
                    <small class="text-muted">${appointment.patient_phone}</small>
                </td>
                <td>
                    <div>${appointment.appointment_date}</div>
                    <small class="text-muted">${appointment.appointment_time}</small>
                </td>
                <td>${app.formatServiceType(appointment.service_type)}</td>
                <td>${appointment.reason || '-'}</td>
                <td>
                    <span class="badge ${statusClass}">
                        ${app.formatStatus(appointment.status)}
                    </span>
                </td>
                <td>
                    <div>${appointment.estimated_cost ? api.formatCurrency(appointment.estimated_cost) : '-'}</div>
                    ${appointment.actual_cost ? `<small class="text-success">Thực: ${api.formatCurrency(appointment.actual_cost)}</small>` : ''}
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="appointments.showViewModal(${appointment.id})" title="Xem chi tiết">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="appointments.showEditModal(${appointment.id})" title="Sửa">
                            <i class="fas fa-edit"></i>
                        </button>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle" data-bs-toggle="dropdown" title="Cập nhật trạng thái">
                                <i class="fas fa-check"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="appointments.updateStatus(${appointment.id}, 'xac_nhan')">Xác nhận</a></li>
                                <li><a class="dropdown-item" href="#" onclick="appointments.updateStatus(${appointment.id}, 'dang_kham')">Đang khám</a></li>
                                <li><a class="dropdown-item" href="#" onclick="appointments.updateStatus(${appointment.id}, 'hoan_thanh')">Hoàn thành</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="appointments.updateStatus(${appointment.id}, 'huy_lich')">Hủy lịch</a></li>
                                <li><a class="dropdown-item text-warning" href="#" onclick="appointments.updateStatus(${appointment.id}, 'khong_den')">Không đến</a></li>
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Get status CSS class
     */
    getStatusClass(status) {
        const statusClasses = {
            'dat_lich': 'status-dat-lich',
            'xac_nhan': 'status-xac-nhan',
            'dang_kham': 'status-dang-kham',
            'hoan_thanh': 'status-hoan-thanh',
            'huy_lich': 'status-huy-lich',
            'khong_den': 'status-khong-den'
        };
        return statusClasses[status] || 'bg-secondary';
    }

    /**
     * Search appointments
     */
    search() {
        const searchInput = document.getElementById('appointmentSearch');
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.loadAppointments();
    }

    /**
     * Filter by status
     */
    filterByStatus() {
        const statusSelect = document.getElementById('statusFilter');
        this.statusFilter = statusSelect.value;
        this.currentPage = 1;
        this.loadAppointments();
    }

    /**
     * Filter by date
     */
    filterByDate() {
        const dateInput = document.getElementById('dateFilter');
        this.dateFilter = dateInput.value;
        this.currentPage = 1;
        this.loadAppointments();
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        document.getElementById('appointmentSearch').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('dateFilter').value = '';
        
        this.searchTerm = '';
        this.statusFilter = '';
        this.dateFilter = '';
        this.currentPage = 1;
        
        this.loadAppointments();
    }

    /**
     * Show add appointment modal
     */
    async showAddModal() {
        // First load patients for selection
        try {
            const patientsResponse = await api.getPatients({ limit: 100 });
            const patients = patientsResponse.success ? patientsResponse.data : [];
            
            this.showAppointmentModal(null, patients);
        } catch (error) {
            api.handleError(error);
        }
    }

    /**
     * Show appointment modal (add/edit)
     */
    showAppointmentModal(appointment = null, patients = []) {
        const isEdit = appointment !== null;
        const title = isEdit ? 'Sửa lịch hẹn' : 'Đặt lịch hẹn mới';
        
        const modalHtml = `
            <div class="modal fade" id="appointmentModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="appointmentForm">
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Bệnh nhân *</label>
                                            <select class="form-select" name="patient_id" required>
                                                <option value="">Chọn bệnh nhân</option>
                                                ${patients.map(patient => `
                                                    <option value="${patient.id}" ${appointment?.patient_id === patient.id ? 'selected' : ''}>
                                                        ${patient.hoten} - ${patient.sdt}
                                                    </option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Dịch vụ</label>
                                            <select class="form-select" name="service_type">
                                                <option value="kham_tong_quat" ${appointment?.service_type === 'kham_tong_quat' ? 'selected' : ''}>Khám tổng quát</option>
                                                <option value="tay_trang" ${appointment?.service_type === 'tay_trang' ? 'selected' : ''}>Tẩy trắng</option>
                                                <option value="nho_rang" ${appointment?.service_type === 'nho_rang' ? 'selected' : ''}>Nhổ răng</option>
                                                <option value="han_tram" ${appointment?.service_type === 'han_tram' ? 'selected' : ''}>Hàn trám</option>
                                                <option value="dieu_tri_tuy" ${appointment?.service_type === 'dieu_tri_tuy' ? 'selected' : ''}>Điều trị tủy</option>
                                                <option value="nieng_rang" ${appointment?.service_type === 'nieng_rang' ? 'selected' : ''}>Niềng răng</option>
                                                <option value="cay_ghep" ${appointment?.service_type === 'cay_ghep' ? 'selected' : ''}>Cấy ghép</option>
                                                <option value="phau_thuat" ${appointment?.service_type === 'phau_thuat' ? 'selected' : ''}>Phẫu thuật</option>
                                                <option value="tu_van" ${appointment?.service_type === 'tu_van' ? 'selected' : ''}>Tư vấn</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Ngày hẹn *</label>
                                            <input type="date" class="form-control" name="appointment_date" 
                                                   value="${appointment?.appointment_date || ''}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Giờ hẹn *</label>
                                            <input type="time" class="form-control" name="appointment_time" 
                                                   value="${appointment?.appointment_time || ''}" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Thời gian (phút)</label>
                                            <input type="number" class="form-control" name="duration" 
                                                   value="${appointment?.duration || 30}" min="15" max="240">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Mức độ ưu tiên</label>
                                            <select class="form-select" name="priority">
                                                <option value="binh_thuong" ${appointment?.priority === 'binh_thuong' ? 'selected' : ''}>Bình thường</option>
                                                <option value="khan_cap" ${appointment?.priority === 'khan_cap' ? 'selected' : ''}>Khẩn cấp</option>
                                                <option value="uu_tien" ${appointment?.priority === 'uu_tien' ? 'selected' : ''}>Ưu tiên</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Lý do khám</label>
                                    <textarea class="form-control" name="reason" rows="2">${appointment?.reason || ''}</textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Chi phí dự kiến (VNĐ)</label>
                                    <input type="number" class="form-control" name="estimated_cost" 
                                           value="${appointment?.estimated_cost || ''}" min="0">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Ghi chú</label>
                                    <textarea class="form-control" name="notes" rows="2">${appointment?.notes || ''}</textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>${isEdit ? 'Cập nhật' : 'Đặt lịch'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to page
        document.getElementById('modalContainer').innerHTML = modalHtml;
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('appointmentModal'));
        modal.show();
        
        // Setup form submission
        document.getElementById('appointmentForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.saveAppointment(appointment?.id, modal);
        });
    }

    /**
     * Save appointment (create or update)
     */
    async saveAppointment(appointmentId, modal) {
        try {
            const form = document.getElementById('appointmentForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            let response;
            if (appointmentId) {
                // Update functionality would need to be implemented in API
                api.showError('Chức năng sửa lịch hẹn chưa được triển khai');
                return;
            } else {
                response = await api.createAppointment(data);
            }
            
            if (response.success) {
                modal.hide();
                api.showSuccess(response.message);
                await this.loadAppointments();
            }
        } catch (error) {
            api.handleError(error);
        }
    }

    /**
     * Update appointment status
     */
    async updateStatus(appointmentId, newStatus) {
        try {
            // This would need to be implemented in the API
            api.showError('Chức năng cập nhật trạng thái chưa được triển khai');
        } catch (error) {
            api.handleError(error);
        }
    }

    /**
     * Render pagination
     */
    renderPagination() {
        const container = document.getElementById('appointmentsPagination');
        if (this.totalPages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // Previous button
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="appointments.goToPage(${this.currentPage - 1})">Trước</a>
            </li>
        `;
        
        // Page numbers
        for (let i = 1; i <= this.totalPages; i++) {
            if (i === this.currentPage || i === 1 || i === this.totalPages || 
                (i >= this.currentPage - 1 && i <= this.currentPage + 1)) {
                html += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="appointments.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 2 || i === this.currentPage + 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // Next button
        html += `
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="appointments.goToPage(${this.currentPage + 1})">Sau</a>
            </li>
        `;
        
        container.innerHTML = html;
    }

    /**
     * Go to specific page
     */
    goToPage(page) {
        if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
            this.currentPage = page;
            this.loadAppointments();
        }
    }
}

// Create global appointments module instance
window.appointments = new AppointmentsModule();
