<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$patient_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$patient_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

// Lấy thông tin bệnh nhân
$database = new Database();
$sql = "SELECT * FROM benhnhan WHERE id = ?";
$patient = $database->fetchOne($sql, [$patient_id]);

if (!$patient) {
    header("Location: list.php?error=not_found");
    exit();
}

// Lấy lịch sử khám bệnh (nếu có)
$history_sql = "SELECT mr.*, d.specialty, u.full_name as doctor_name 
                FROM medical_records mr 
                LEFT JOIN doctors d ON mr.doctor_id = d.id 
                LEFT JOIN users u ON d.user_id = u.id 
                WHERE mr.patient_id = ? 
                ORDER BY mr.visit_date DESC";
$medical_history = $database->fetchAll($history_sql, [$patient_id]);
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thông tin Bệnh nhân - <?php echo htmlspecialchars($patient['hoten']); ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .patient-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px auto;
            max-width: 1000px;
        }
        
        .patient-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .patient-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 4px solid #4CAF50;
        }
        
        .info-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .info-item {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-start;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
            min-width: 120px;
            margin-right: 15px;
        }
        
        .info-value {
            color: #333;
            flex: 1;
        }
        
        .history-section {
            margin-top: 40px;
        }
        
        .history-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .home-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
            background: linear-gradient(45deg, #ee5a52, #ff6b6b);
        }
        
        @media (max-width: 768px) {
            .patient-info {
                grid-template-columns: 1fr;
            }
            
            .patient-header {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="../../dashboard.php" class="home-btn">
        <span style="font-size: 20px;">🏠</span>
        <span>Trang chủ</span>
    </a>
    
    <div class="patient-container fade-in">
        <div class="patient-header">
            <h1>
                <img src="../../assets/gifs/patient-icon.gif" alt="Bệnh nhân" style="width: 40px; height: 40px; vertical-align: middle; margin-right: 10px;">
                Thông tin Bệnh nhân
            </h1>
            <div>
                <a href="edit.php?id=<?php echo $patient['id']; ?>" class="btn btn-warning">✏️ Chỉnh sửa</a>
                <?php if ($current_user['role'] == 'dentist'): ?>
                    <a href="delete.php?id=<?php echo $patient['id']; ?>"
                       class="btn btn-danger"
                       onclick="return confirm('⚠️ Bạn có chắc muốn xóa bệnh nhân này?\n\nTất cả lịch hẹn và hồ sơ khám bệnh sẽ bị xóa vĩnh viễn!\n\nHành động này không thể hoàn tác!')"
                       style="background: linear-gradient(45deg, #ff6b6b, #ee5a52);">🗑️ Xóa bệnh nhân</a>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="patient-info">
            <!-- Thông tin cơ bản -->
            <div class="info-section">
                <h3>📋 Thông tin cơ bản</h3>
                <div class="info-item">
                    <span class="info-label">Họ tên:</span>
                    <span class="info-value"><?php echo htmlspecialchars($patient['hoten']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Số điện thoại:</span>
                    <span class="info-value"><?php echo htmlspecialchars($patient['sdt']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email:</span>
                    <span class="info-value"><?php echo htmlspecialchars($patient['email'] ?: 'Chưa có'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Ngày sinh:</span>
                    <span class="info-value">
                        <?php 
                        if ($patient['ngaysinh']) {
                            $birth_date = new DateTime($patient['ngaysinh']);
                            $today = new DateTime();
                            $age = $today->diff($birth_date)->y;
                            echo $birth_date->format('d/m/Y') . " (Tuổi: $age)";
                        } else {
                            echo 'Chưa có';
                        }
                        ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Giới tính:</span>
                    <span class="info-value"><?php echo htmlspecialchars($patient['gioitinh'] ?: 'Chưa có'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">CMND/CCCD:</span>
                    <span class="info-value"><?php echo htmlspecialchars($patient['cmnd'] ?: 'Chưa có'); ?></span>
                </div>
            </div>
            
            <!-- Thông tin liên hệ -->
            <div class="info-section">
                <h3>📍 Thông tin liên hệ</h3>
                <div class="info-item">
                    <span class="info-label">Địa chỉ:</span>
                    <span class="info-value"><?php echo htmlspecialchars($patient['diachi'] ?: 'Chưa có'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Nghề nghiệp:</span>
                    <span class="info-value"><?php echo htmlspecialchars($patient['nghe_nghiep'] ?: 'Chưa có'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Liên hệ khẩn cấp:</span>
                    <span class="info-value"><?php echo htmlspecialchars($patient['lien_he_khan_cap'] ?: 'Chưa có'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Ngày tạo:</span>
                    <span class="info-value">
                        <?php echo date('d/m/Y H:i', strtotime($patient['created_at'])); ?>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Thông tin y tế -->
        <div class="info-section" style="grid-column: 1 / -1;">
            <h3>🏥 Thông tin y tế</h3>
            <div class="info-item">
                <span class="info-label">Tiền sử bệnh:</span>
                <span class="info-value"><?php echo nl2br(htmlspecialchars($patient['tien_su_benh'] ?: 'Không có')); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Dị ứng:</span>
                <span class="info-value"><?php echo nl2br(htmlspecialchars($patient['di_ung'] ?: 'Không có')); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Ghi chú:</span>
                <span class="info-value"><?php echo nl2br(htmlspecialchars($patient['ghi_chu'] ?: 'Không có')); ?></span>
            </div>
        </div>
        
        <!-- Lịch sử khám bệnh -->
        <div class="history-section">
            <h3>📋 Lịch sử khám bệnh</h3>
            <?php if (empty($medical_history)): ?>
                <div style="text-align: center; padding: 40px; color: #666; background: #f8f9fa; border-radius: 10px;">
                    Chưa có lịch sử khám bệnh
                </div>
            <?php else: ?>
                <?php foreach ($medical_history as $record): ?>
                    <div class="history-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <strong>Ngày khám: <?php echo date('d/m/Y', strtotime($record['visit_date'])); ?></strong>
                            <span>Bác sĩ: <?php echo htmlspecialchars($record['doctor_name']); ?></span>
                        </div>
                        <div><strong>Triệu chứng:</strong> <?php echo htmlspecialchars($record['symptoms']); ?></div>
                        <div><strong>Chẩn đoán:</strong> <?php echo htmlspecialchars($record['diagnosis']); ?></div>
                        <div><strong>Điều trị:</strong> <?php echo htmlspecialchars($record['treatment']); ?></div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.patient-container').classList.add('fade-in');
        });
    </script>
</body>
</html>
