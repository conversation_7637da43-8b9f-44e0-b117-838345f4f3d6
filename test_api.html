<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Phòng khám Nha khoa</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-result {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 0.375rem 0.375rem 0;
        }
        .test-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .test-error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        pre {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 0.375rem;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">🧪 API Testing Tool</h1>
        <p class="lead">Công cụ test các API endpoints của hệ thống quản lý phòng khám</p>
        
        <!-- Authentication Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>🔐 Authentication Tests</h3>
            </div>
            <div class="card-body">
                <button class="btn btn-primary me-2" onclick="testLogin()">Test Login</button>
                <button class="btn btn-secondary me-2" onclick="testVerifyToken()">Test Verify Token</button>
                <button class="btn btn-warning" onclick="testLogout()">Test Logout</button>
                
                <div id="authResults"></div>
            </div>
        </div>
        
        <!-- Patients API Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>👥 Patients API Tests</h3>
            </div>
            <div class="card-body">
                <button class="btn btn-info me-2" onclick="testGetPatients()">Get Patients</button>
                <button class="btn btn-success me-2" onclick="testCreatePatient()">Create Patient</button>
                <button class="btn btn-warning me-2" onclick="testUpdatePatient()">Update Patient</button>
                <button class="btn btn-danger" onclick="testDeletePatient()">Delete Patient</button>
                
                <div id="patientsResults"></div>
            </div>
        </div>
        
        <!-- Appointments API Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>📅 Appointments API Tests</h3>
            </div>
            <div class="card-body">
                <button class="btn btn-info me-2" onclick="testGetAppointments()">Get Appointments</button>
                <button class="btn btn-success" onclick="testCreateAppointment()">Create Appointment</button>
                
                <div id="appointmentsResults"></div>
            </div>
        </div>
        
        <!-- Reports API Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>📊 Reports API Tests</h3>
            </div>
            <div class="card-body">
                <button class="btn btn-info" onclick="testGetStats()">Get Statistics</button>
                
                <div id="reportsResults"></div>
            </div>
        </div>
        
        <!-- Test Results Summary -->
        <div class="card">
            <div class="card-header">
                <h3>📋 Test Summary</h3>
            </div>
            <div class="card-body">
                <div id="testSummary">
                    <p>Chưa có test nào được chạy</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let authToken = null;
        let testResults = [];
        
        const API_BASE = './api';
        
        // Utility functions
        function logTest(name, success, response, error = null) {
            testResults.push({ name, success, response, error, timestamp: new Date() });
            updateTestSummary();
        }
        
        function updateTestSummary() {
            const summary = document.getElementById('testSummary');
            const total = testResults.length;
            const passed = testResults.filter(t => t.success).length;
            const failed = total - passed;
            
            summary.innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-primary">${total}</h4>
                            <p>Tổng tests</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-success">${passed}</h4>
                            <p>Thành công</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-danger">${failed}</h4>
                            <p>Thất bại</p>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function displayResult(containerId, testName, success, data, error = null) {
            const container = document.getElementById(containerId);
            const resultClass = success ? 'test-success' : 'test-error';
            const icon = success ? '✅' : '❌';
            
            const resultHtml = `
                <div class="test-result ${resultClass}">
                    <h5>${icon} ${testName}</h5>
                    <p><strong>Status:</strong> ${success ? 'SUCCESS' : 'FAILED'}</p>
                    ${error ? `<p><strong>Error:</strong> ${error}</p>` : ''}
                    <details>
                        <summary>Response Data</summary>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </details>
                </div>
            `;
            
            container.innerHTML = resultHtml + container.innerHTML;
        }
        
        async function makeRequest(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                ...options
            };
            
            if (authToken && options.auth !== false) {
                config.headers['Authorization'] = `Bearer ${authToken}`;
            }
            
            if (config.method !== 'GET' && options.data) {
                config.body = JSON.stringify(options.data);
            }
            
            const response = await fetch(url, config);
            const data = await response.json();
            
            return { response, data };
        }
        
        // Authentication Tests
        async function testLogin() {
            try {
                const { response, data } = await makeRequest('/auth/login.php', {
                    method: 'POST',
                    data: {
                        username: 'dr.nam',
                        password: '123456'
                    },
                    auth: false
                });
                
                const success = response.ok && data.success;
                if (success && data.data.token) {
                    authToken = data.data.token;
                }
                
                logTest('Login API', success, data);
                displayResult('authResults', 'Login Test', success, data);
                
            } catch (error) {
                logTest('Login API', false, null, error.message);
                displayResult('authResults', 'Login Test', false, null, error.message);
            }
        }
        
        async function testVerifyToken() {
            if (!authToken) {
                displayResult('authResults', 'Verify Token Test', false, null, 'No token available. Please login first.');
                return;
            }
            
            try {
                const { response, data } = await makeRequest('/auth/verify.php');
                const success = response.ok && data.success;
                
                logTest('Verify Token API', success, data);
                displayResult('authResults', 'Verify Token Test', success, data);
                
            } catch (error) {
                logTest('Verify Token API', false, null, error.message);
                displayResult('authResults', 'Verify Token Test', false, null, error.message);
            }
        }
        
        async function testLogout() {
            try {
                const { response, data } = await makeRequest('/auth/logout.php', {
                    method: 'POST'
                });
                
                const success = response.ok && data.success;
                if (success) {
                    authToken = null;
                }
                
                logTest('Logout API', success, data);
                displayResult('authResults', 'Logout Test', success, data);
                
            } catch (error) {
                logTest('Logout API', false, null, error.message);
                displayResult('authResults', 'Logout Test', false, null, error.message);
            }
        }
        
        // Patients Tests
        async function testGetPatients() {
            try {
                const { response, data } = await makeRequest('/patients/index.php?page=1&limit=5');
                const success = response.ok && data.success;
                
                logTest('Get Patients API', success, data);
                displayResult('patientsResults', 'Get Patients Test', success, data);
                
            } catch (error) {
                logTest('Get Patients API', false, null, error.message);
                displayResult('patientsResults', 'Get Patients Test', false, null, error.message);
            }
        }
        
        async function testCreatePatient() {
            try {
                const testPatient = {
                    hoten: 'Test Patient ' + Date.now(),
                    sdt: '**********',
                    email: '<EMAIL>',
                    ngaysinh: '1990-01-01',
                    gioitinh: 'Nam',
                    diachi: 'Test Address'
                };
                
                const { response, data } = await makeRequest('/patients/index.php', {
                    method: 'POST',
                    data: testPatient
                });
                
                const success = response.ok && data.success;
                
                logTest('Create Patient API', success, data);
                displayResult('patientsResults', 'Create Patient Test', success, data);
                
            } catch (error) {
                logTest('Create Patient API', false, null, error.message);
                displayResult('patientsResults', 'Create Patient Test', false, null, error.message);
            }
        }
        
        async function testUpdatePatient() {
            // This would need a real patient ID
            displayResult('patientsResults', 'Update Patient Test', false, null, 'Need real patient ID for testing');
        }
        
        async function testDeletePatient() {
            // This would need a real patient ID and dentist role
            displayResult('patientsResults', 'Delete Patient Test', false, null, 'Need real patient ID and dentist role for testing');
        }
        
        // Appointments Tests
        async function testGetAppointments() {
            try {
                const { response, data } = await makeRequest('/appointments/index.php?page=1&limit=5');
                const success = response.ok && data.success;
                
                logTest('Get Appointments API', success, data);
                displayResult('appointmentsResults', 'Get Appointments Test', success, data);
                
            } catch (error) {
                logTest('Get Appointments API', false, null, error.message);
                displayResult('appointmentsResults', 'Get Appointments Test', false, null, error.message);
            }
        }
        
        async function testCreateAppointment() {
            try {
                const testAppointment = {
                    patient_id: 1, // Assuming patient ID 1 exists
                    appointment_date: '2024-12-30',
                    appointment_time: '10:00',
                    service_type: 'kham_tong_quat',
                    reason: 'Test appointment'
                };
                
                const { response, data } = await makeRequest('/appointments/index.php', {
                    method: 'POST',
                    data: testAppointment
                });
                
                const success = response.ok && data.success;
                
                logTest('Create Appointment API', success, data);
                displayResult('appointmentsResults', 'Create Appointment Test', success, data);
                
            } catch (error) {
                logTest('Create Appointment API', false, null, error.message);
                displayResult('appointmentsResults', 'Create Appointment Test', false, null, error.message);
            }
        }
        
        // Reports Tests
        async function testGetStats() {
            try {
                const { response, data } = await makeRequest('/reports/stats.php');
                const success = response.ok && data.success;
                
                logTest('Get Statistics API', success, data);
                displayResult('reportsResults', 'Get Statistics Test', success, data);
                
            } catch (error) {
                logTest('Get Statistics API', false, null, error.message);
                displayResult('reportsResults', 'Get Statistics Test', false, null, error.message);
            }
        }
        
        // Auto-run basic tests on page load
        window.addEventListener('load', () => {
            console.log('API Test Tool loaded. Click buttons to test individual endpoints.');
        });
    </script>
</body>
</html>
