/* Reset và Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Login Container */
.login-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    max-width: 900px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 500px;
}

/* Welcome Section với GIF */
.welcome-section {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    padding: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.welcome-gif {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin-bottom: 20px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.welcome-section h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Login Form */
.login-form {
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.login-form h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
    font-size: 28px;
    font-weight: 300;
}

.input-group {
    margin-bottom: 20px;
}

.input-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e1e1;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.input-group input:focus {
    outline: none;
    border-color: #4CAF50;
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
}

/* Login Button với Loading GIF */
.login-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(76, 175, 80, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.loading-gif {
    width: 20px;
    height: 20px;
}

/* Error Message */
.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 15px;
    border-radius: 10px;
    margin-top: 20px;
    border-left: 4px solid #c62828;
    text-align: center;
}

/* Dashboard Styles */
.dashboard-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin: 20px;
    max-width: 800px;
    margin: 20px auto;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 40px;
}

.dashboard-gif {
    width: 100px;
    height: 100px;
    margin: 0 auto 20px;
    display: block;
    border-radius: 50%;
}

.dashboard-menu {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.dashboard-menu li {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.dashboard-menu li:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
}

.dashboard-menu a {
    display: block;
    padding: 20px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    border-radius: 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        grid-template-columns: 1fr;
        max-width: 400px;
        margin: 20px;
    }
    
    .welcome-section {
        padding: 30px 20px;
    }
    
    .welcome-gif {
        width: 100px;
        height: 100px;
    }
    
    .welcome-section h1 {
        font-size: 20px;
    }
    
    .login-form {
        padding: 30px 20px;
    }
}

/* Animation cho các element */
.fade-in {
    animation: fadeIn 0.8s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
