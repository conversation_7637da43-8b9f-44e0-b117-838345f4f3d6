<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();

if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    header("Location: list.php?error=invalid_request");
    exit();
}

$appointment_id = isset($_POST['appointment_id']) ? (int)$_POST['appointment_id'] : 0;
$new_status = isset($_POST['status']) ? trim($_POST['status']) : '';

if (!$appointment_id || !$new_status) {
    header("Location: list.php?error=invalid_data");
    exit();
}

// Kiểm tra status hợp lệ
$valid_statuses = ['dat_lich', 'xac_nhan', 'dang_kham', 'hoan_thanh', 'huy_lich', 'khong_den'];
if (!in_array($new_status, $valid_statuses)) {
    header("Location: view.php?id=$appointment_id&error=invalid_status");
    exit();
}

$database = new Database();

try {
    // Kiểm tra lịch hẹn tồn tại
    $appointment = $database->fetchOne("SELECT * FROM appointments WHERE id = ?", [$appointment_id]);
    
    if (!$appointment) {
        header("Location: list.php?error=not_found");
        exit();
    }
    
    // Cập nhật trạng thái
    $sql = "UPDATE appointments SET status = ?, updated_at = NOW() WHERE id = ?";
    $result = $database->execute($sql, [$new_status, $appointment_id]);
    
    if ($result) {
        // Tạo thông báo thành công
        $status_messages = [
            'dat_lich' => 'Đã chuyển về trạng thái "Đã đặt"',
            'xac_nhan' => 'Đã xác nhận lịch hẹn',
            'dang_kham' => 'Đã bắt đầu khám bệnh',
            'hoan_thanh' => 'Đã hoàn thành lịch hẹn',
            'huy_lich' => 'Đã hủy lịch hẹn',
            'khong_den' => 'Đã đánh dấu "Không đến"'
        ];
        
        $message = $status_messages[$new_status] ?? 'Đã cập nhật trạng thái';
        header("Location: view.php?id=$appointment_id&success=" . urlencode($message));
    } else {
        header("Location: view.php?id=$appointment_id&error=update_failed");
    }
    
} catch(Exception $e) {
    error_log("Error updating appointment status: " . $e->getMessage());
    header("Location: view.php?id=$appointment_id&error=system_error");
}

exit();
?>
