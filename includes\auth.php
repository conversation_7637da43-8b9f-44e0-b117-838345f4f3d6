<?php
/**
 * <PERSON><PERSON> lý xác thực và phân quyền
 * Phòng khám Management System
 */

require_once __DIR__ . '/../config/database.php';

class Auth {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    /**
     * Đăng nhập cho bác sĩ nha khoa tư nhân
     */
    public function login($username, $password) {
        try {
            $sql = "SELECT * FROM users WHERE username = ? AND status = 'active'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$username]);
            $user = $stmt->fetch();

            // Kiểm tra mật khẩu (hỗ trợ cả plain text và hash)
            $password_valid = false;
            if ($user) {
                if (password_verify($password, $user['password'])) {
                    $password_valid = true;
                } elseif ($user['password'] === $password) {
                    // Mật khẩu plain text - hash lại ngay
                    $hashed = password_hash($password, PASSWORD_DEFAULT);
                    $update_sql = "UPDATE users SET password = ? WHERE id = ?";
                    $update_stmt = $this->db->prepare($update_sql);
                    $update_stmt->execute([$hashed, $user['id']]);
                    $password_valid = true;
                }
            }

            if ($user && $password_valid) {
                // Tạo session
                if (session_status() == PHP_SESSION_NONE) {
                    session_start();
                }

                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['phone'] = $user['phone'];
                $_SESSION['logged_in'] = true;

                // Thông tin phòng khám (cho bác sĩ)
                if ($user['role'] == 'dentist') {
                    $_SESSION['clinic_name'] = $user['clinic_name'];
                    $_SESSION['clinic_address'] = $user['clinic_address'];
                    $_SESSION['clinic_phone'] = $user['clinic_phone'];
                    $_SESSION['license_number'] = $user['license_number'];
                    $_SESSION['specialty'] = $user['specialty'];
                    $_SESSION['experience_years'] = $user['experience_years'];
                }

                // Cập nhật thời gian đăng nhập cuối
                $this->updateLastLogin($user['id']);

                return [
                    'success' => true,
                    'user' => $user,
                    'message' => 'Đăng nhập thành công!'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Tên đăng nhập hoặc mật khẩu không đúng!'
                ];
            }
        } catch(PDOException $e) {
            return [
                'success' => false,
                'message' => 'Lỗi hệ thống: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Đăng xuất
     */
    public function logout() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        session_destroy();
        return true;
    }

    /**
     * Kiểm tra đăng nhập
     */
    public function isLoggedIn() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
    }

    /**
     * Kiểm tra quyền
     */
    public function hasRole($required_roles) {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        if (!$this->isLoggedIn()) {
            return false;
        }

        $user_role = $_SESSION['role'];

        if (is_string($required_roles)) {
            return $user_role === $required_roles;
        }

        if (is_array($required_roles)) {
            return in_array($user_role, $required_roles);
        }

        return false;
    }

    /**
     * Lấy thông tin user hiện tại
     */
    public function getCurrentUser() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        if (!$this->isLoggedIn()) {
            return null;
        }

        return [
            'id' => $_SESSION['user_id'] ?? null,
            'username' => $_SESSION['username'] ?? null,
            'full_name' => $_SESSION['full_name'] ?? null,
            'role' => $_SESSION['role'] ?? null,
            'email' => $_SESSION['email'] ?? null,
            'phone' => $_SESSION['phone'] ?? null,
            'clinic_name' => $_SESSION['clinic_name'] ?? null,
            'clinic_address' => $_SESSION['clinic_address'] ?? null,
            'clinic_phone' => $_SESSION['clinic_phone'] ?? null,
            'license_number' => $_SESSION['license_number'] ?? null,
            'specialty' => $_SESSION['specialty'] ?? null,
            'experience_years' => $_SESSION['experience_years'] ?? null
        ];
    }

    /**
     * Yêu cầu đăng nhập
     */
    public function requireLogin($redirect_url = '../index.php') {
        if (!$this->isLoggedIn()) {
            header("Location: $redirect_url");
            exit();
        }
    }

    /**
     * Yêu cầu quyền
     */
    public function requireRole($required_roles, $redirect_url = '../dashboard.php') {
        $this->requireLogin();
        
        if (!$this->hasRole($required_roles)) {
            header("Location: $redirect_url?error=access_denied");
            exit();
        }
    }

    /**
     * Đổi mật khẩu
     */
    public function changePassword($user_id, $old_password, $new_password) {
        try {
            // Kiểm tra mật khẩu cũ
            $sql = "SELECT password FROM users WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($old_password, $user['password'])) {
                return [
                    'success' => false,
                    'message' => 'Mật khẩu cũ không đúng!'
                ];
            }

            // Cập nhật mật khẩu mới
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $sql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            
            if ($stmt->execute([$hashed_password, $user_id])) {
                return [
                    'success' => true,
                    'message' => 'Đổi mật khẩu thành công!'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Lỗi khi cập nhật mật khẩu!'
                ];
            }
        } catch(PDOException $e) {
            return [
                'success' => false,
                'message' => 'Lỗi hệ thống: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Cập nhật thời gian đăng nhập cuối
     */
    private function updateLastLogin($user_id) {
        try {
            $sql = "UPDATE users SET updated_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$user_id]);
        } catch(PDOException $e) {
            // Log error nhưng không làm gián đoạn quá trình đăng nhập
            error_log("Update last login error: " . $e->getMessage());
        }
    }

    /**
     * Tạo mật khẩu hash
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * Kiểm tra mật khẩu
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
}
