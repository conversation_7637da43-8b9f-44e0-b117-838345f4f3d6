# Hệ thống Quản lý Phòng khám Nha khoa - API Version

## 📋 Tổng quan

Đây là phiên bản API-driven của hệ thống quản lý phòng khám nha khoa, đ<PERSON><PERSON><PERSON> phát triển để đáp ứng các yêu cầu học thuật về:

- ✅ **Quản trị cơ sở dữ liệu**: MySQL với 5 bảng chính
- ✅ **Xây dựng API phía Backend**: RESTful API với JSON responses
- ✅ **Xây dựng ứng dụng phía Frontend**: Modern HTML5/CSS3/JavaScript SPA
- ✅ **Tích hợp Backend-Frontend**: AJAX-based communication với API

## 🏗️ Kiến trúc Hệ thống

### Backend API Layer
```
api/
├── config/
│   ├── cors.php          # CORS configuration
│   ├── response.php      # Standardized JSON responses
│   └── middleware.php    # Authentication & authorization
├── auth/
│   ├── login.php         # POST /api/auth/login
│   ├── logout.php        # POST /api/auth/logout
│   └── verify.php        # GET /api/auth/verify
├── patients/
│   ├── index.php         # GET/POST /api/patients
│   ├── update.php        # PUT /api/patients/{id}
│   └── delete.php        # DELETE /api/patients/{id}
├── appointments/
│   └── index.php         # GET/POST /api/appointments
└── reports/
    └── stats.php         # GET /api/reports/stats
```

### Frontend Application
```
frontend/
├── index.html            # Single Page Application
├── assets/css/app.css    # Modern responsive CSS
└── js/
    ├── api.js            # API client & HTTP requests
    ├── auth.js           # Authentication management
    ├── app.js            # Main application controller
    ├── patients.js       # Patient management module
    ├── appointments.js   # Appointment management module
    └── reports.js        # Reports & statistics module
```

## 🚀 Cài đặt và Triển khai

### Yêu cầu hệ thống
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx với mod_rewrite
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Bước 1: Cấu hình Database
Database hiện tại đã được tối ưu và sẵn sàng sử dụng với 5 bảng:
- `users` - Quản lý người dùng hệ thống
- `benhnhan` - Thông tin bệnh nhân
- `appointments` - Lịch hẹn khám
- `medical_records` - Hồ sơ y tế
- `doctor_schedules` - Lịch làm việc bác sĩ

### Bước 2: Cấu hình API Backend
1. Đảm bảo file `config/database.php` có thông tin kết nối đúng
2. Kiểm tra CORS settings trong `api/config/cors.php`
3. Test API endpoints:
   ```bash
   # Test authentication
   curl -X POST http://localhost/phongkham/api/auth/login.php \
        -H "Content-Type: application/json" \
        -d '{"username":"dr.nam","password":"123456"}'
   
   # Test patients API
   curl -X GET http://localhost/phongkham/api/patients/index.php \
        -H "Authorization: Bearer YOUR_TOKEN"
   ```

### Bước 3: Triển khai Frontend
1. Mở `frontend/index.html` trong trình duyệt
2. Đăng nhập với tài khoản demo: `dr.nam` / `123456`
3. Kiểm tra các chức năng:
   - Dashboard với thống kê tổng quan
   - Quản lý bệnh nhân (CRUD operations)
   - Quản lý lịch hẹn
   - Báo cáo và thống kê

## 🔧 API Documentation

### Authentication Endpoints

#### POST /api/auth/login.php
Đăng nhập người dùng
```json
Request:
{
  "username": "dr.nam",
  "password": "123456"
}

Response:
{
  "success": true,
  "message": "Đăng nhập thành công",
  "data": {
    "token": "session_token_here",
    "user": {
      "id": 1,
      "username": "dr.nam",
      "full_name": "Dr. Nam",
      "role": "dentist"
    }
  }
}
```

#### GET /api/auth/verify.php
Xác thực token hiện tại
```json
Headers: Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "user": {...}
  }
}
```

### Patients Endpoints

#### GET /api/patients/index.php
Lấy danh sách bệnh nhân với phân trang
```
Query Parameters:
- page: Trang hiện tại (default: 1)
- limit: Số bản ghi mỗi trang (default: 10)
- search: Từ khóa tìm kiếm

Response:
{
  "success": true,
  "data": [...],
  "pagination": {
    "current_page": 1,
    "total_pages": 5,
    "total_records": 50
  }
}
```

#### POST /api/patients/index.php
Tạo bệnh nhân mới
```json
Request:
{
  "hoten": "Nguyễn Văn A",
  "sdt": "**********",
  "email": "<EMAIL>",
  "ngaysinh": "1990-01-01",
  "gioitinh": "Nam",
  "diachi": "123 ABC Street"
}
```

#### PUT /api/patients/update.php?id={id}
Cập nhật thông tin bệnh nhân

#### DELETE /api/patients/delete.php?id={id}
Xóa bệnh nhân (chỉ dentist role)

### Appointments Endpoints

#### GET /api/appointments/index.php
Lấy danh sách lịch hẹn với bộ lọc
```
Query Parameters:
- page, limit: Phân trang
- search: Tìm kiếm
- status: Lọc theo trạng thái
- date_from, date_to: Lọc theo khoảng thời gian
```

#### POST /api/appointments/index.php
Tạo lịch hẹn mới
```json
Request:
{
  "patient_id": 1,
  "appointment_date": "2024-01-15",
  "appointment_time": "09:00",
  "service_type": "kham_tong_quat",
  "reason": "Khám định kỳ"
}
```

### Reports Endpoints

#### GET /api/reports/stats.php
Lấy thống kê và báo cáo
```
Query Parameters:
- date_from, date_to: Khoảng thời gian thống kê

Response:
{
  "success": true,
  "data": {
    "total_patients": 150,
    "today_appointments": 8,
    "completed_today": 5,
    "revenue": 15000000,
    "appointments_by_status": {...},
    "services": [...],
    "monthly_trend": [...],
    "recent_patients": [...],
    "upcoming_appointments": [...]
  }
}
```

## 🎯 Tính năng Chính

### 1. Authentication & Authorization
- Token-based authentication
- Role-based access control (dentist/assistant)
- Session management
- Secure logout

### 2. Patient Management
- CRUD operations với validation
- Advanced search và filtering
- Pagination cho hiệu suất
- Responsive UI với Bootstrap 5

### 3. Appointment Scheduling
- Tạo và quản lý lịch hẹn
- Multiple status tracking
- Service type categorization
- Date/time conflict detection

### 4. Reports & Analytics
- Real-time dashboard statistics
- Appointment status breakdown
- Service popularity analysis
- Revenue tracking
- Monthly trend analysis

### 5. Modern Frontend Features
- Single Page Application (SPA)
- AJAX-based data loading
- Responsive design
- Loading states và error handling
- Modal-based forms
- Real-time notifications

## 🔒 Bảo mật

- CORS configuration cho cross-origin requests
- SQL injection prevention với prepared statements
- XSS protection với input sanitization
- Authentication middleware cho tất cả protected endpoints
- Role-based authorization

## 📱 Responsive Design

Frontend được thiết kế responsive với:
- Bootstrap 5 framework
- Mobile-first approach
- Touch-friendly interface
- Adaptive layouts cho tablet và mobile

## 🚀 Performance

- Pagination để giảm tải database
- Lazy loading cho modules
- Optimized SQL queries
- Minimal HTTP requests
- CSS/JS minification ready

## 📞 Hỗ trợ

Hệ thống đã được thiết kế và triển khai đầy đủ theo yêu cầu học thuật. Tất cả các API endpoints đều hoạt động và frontend đã được tích hợp hoàn chỉnh.

Để test hệ thống:
1. Truy cập `frontend/index.html`
2. Đăng nhập: `dr.nam` / `123456`
3. Khám phá các tính năng trong dashboard

---
**Phát triển bởi**: Dental Clinic Management System Team  
**Phiên bản**: 2.0 (API-driven)  
**Ngày cập nhật**: 2024
