<?php
/**
 * API Middleware for Authentication
 * Phòng khám Management System API
 */

class ApiMiddleware {
    
    /**
     * Verify API token and return user info
     */
    public static function authenticate() {
        // Get token from header or query parameter
        $token = null;
        
        // Check Authorization header
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            $auth_header = $headers['Authorization'];
            if (strpos($auth_header, 'Bearer ') === 0) {
                $token = substr($auth_header, 7);
            }
        }
        
        // Check query parameter as fallback
        if (!$token && isset($_GET['token'])) {
            $token = $_GET['token'];
        }
        
        if (!$token) {
            ApiResponse::error('Token không được cung cấp', 401);
        }
        
        // Verify token
        session_start();
        if (!isset($_SESSION['api_token']) || $_SESSION['api_token'] !== $token) {
            ApiResponse::error('Token không hợp lệ hoặc đã hết hạn', 401);
        }
        
        // Get user info
        $database = new Database();
        $user = $database->fetchOne(
            "SELECT id, username, full_name, role, email, clinic_name FROM users WHERE id = ? AND status = 'active'",
            [$_SESSION['api_user_id']]
        );
        
        if (!$user) {
            ApiResponse::error('Người dùng không tồn tại hoặc đã bị khóa', 401);
        }
        
        return $user;
    }
    
    /**
     * Check if user has required role
     */
    public static function requireRole($user, $required_role) {
        if ($user['role'] !== $required_role) {
            ApiResponse::error('Không có quyền truy cập', 403);
        }
    }
    
    /**
     * Check if user is dentist (admin role)
     */
    public static function requireDentist($user) {
        self::requireRole($user, 'dentist');
    }
    
    /**
     * Validate pagination parameters
     */
    public static function validatePagination() {
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        
        // Validate ranges
        $page = max(1, $page);
        $limit = max(1, min(100, $limit)); // Max 100 records per page
        
        return [
            'page' => $page,
            'limit' => $limit,
            'offset' => ($page - 1) * $limit
        ];
    }
    
    /**
     * Parse search parameters
     */
    public static function parseSearch() {
        return isset($_GET['search']) ? trim($_GET['search']) : '';
    }
}
?>
