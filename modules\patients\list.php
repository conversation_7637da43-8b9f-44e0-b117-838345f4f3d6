<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();

// X<PERSON> lý tìm kiếm
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Xây dựng câu truy vấn
$where_clause = '';
$params = [];

if (!empty($search)) {
    $where_clause = "WHERE hoten LIKE ? OR sdt LIKE ? OR email LIKE ? OR cmnd LIKE ?";
    $search_param = "%$search%";
    $params = [$search_param, $search_param, $search_param, $search_param];
}

// Đếm tổng số bản ghi
$count_sql = "SELECT COUNT(*) as total FROM benhnhan $where_clause";
$database = new Database();
$total_records = $database->fetchOne($count_sql, $params)['total'];
$total_pages = ceil($total_records / $limit);

// Lấy danh sách bệnh nhân
$sql = "SELECT * FROM benhnhan $where_clause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$patients = $database->fetchAll($sql, $params);

// Xử lý thông báo
$success = '';
$error = '';
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'deleted':
            $success = "Xóa bệnh nhân thành công!";
            break;
    }
}
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'invalid_id':
            $error = "ID bệnh nhân không hợp lệ!";
            break;
        case 'not_found':
            $error = "Không tìm thấy bệnh nhân!";
            break;
        case 'permission_denied':
            $error = "Bạn không có quyền xóa bệnh nhân! Chỉ bác sĩ mới có thể thực hiện chức năng này.";
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý Bệnh nhân - Phòng khám</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/icons.css">
    <style>
        .module-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
        }
        
        .module-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .search-box {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-box input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(76, 175, 80, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        th {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        
        .pagination a {
            padding: 10px 15px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            text-decoration: none;
            color: #4CAF50;
            font-weight: 600;
        }
        
        .pagination a.active {
            background: #4CAF50;
            color: white;
        }
        
        .home-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
            background: linear-gradient(45deg, #ee5a52, #ff6b6b);
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="../../dashboard.php" class="home-btn">
        <span style="font-size: 20px;">🏠</span>
        <span>Trang chủ</span>
    </a>
    
    <div class="module-container fade-in">
        <div class="module-header">
            <h1>
                <div class="icon-replacement patient-icon">👤</div>
                Quản lý Bệnh nhân
            </h1>
            <a href="add.php" class="btn btn-primary">+ Thêm bệnh nhân mới</a>
        </div>

        <!-- Thông báo -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <!-- Tìm kiếm -->
        <form method="GET" class="search-box">
            <input type="text" name="search" placeholder="Tìm kiếm theo tên, số điện thoại, email, CMND..." 
                   value="<?php echo htmlspecialchars($search); ?>">
            <button type="submit" class="btn btn-primary">Tìm kiếm</button>
            <?php if (!empty($search)): ?>
                <a href="list.php" class="btn btn-secondary">Xóa bộ lọc</a>
            <?php endif; ?>
        </form>
        
        <!-- Bảng danh sách -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Họ tên</th>
                        <th>Số điện thoại</th>
                        <th>Email</th>
                        <th>Ngày sinh</th>
                        <th>Giới tính</th>
                        <th>Địa chỉ</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($patients)): ?>
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                                <?php if (!empty($search)): ?>
                                    Không tìm thấy bệnh nhân nào với từ khóa "<?php echo htmlspecialchars($search); ?>"
                                <?php else: ?>
                                    Chưa có bệnh nhân nào trong hệ thống
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($patients as $patient): ?>
                            <tr>
                                <td><?php echo $patient['id']; ?></td>
                                <td><?php echo htmlspecialchars($patient['hoten']); ?></td>
                                <td><?php echo htmlspecialchars($patient['sdt']); ?></td>
                                <td><?php echo htmlspecialchars($patient['email']); ?></td>
                                <td><?php echo $patient['ngaysinh'] ? date('d/m/Y', strtotime($patient['ngaysinh'])) : ''; ?></td>
                                <td><?php echo htmlspecialchars($patient['gioitinh']); ?></td>
                                <td><?php echo htmlspecialchars($patient['diachi']); ?></td>
                                <td>
                                    <a href="view.php?id=<?php echo $patient['id']; ?>" class="btn btn-secondary" style="padding: 8px 12px; font-size: 14px;">Xem</a>
                                    <a href="edit.php?id=<?php echo $patient['id']; ?>" class="btn btn-primary" style="padding: 8px 12px; font-size: 14px;">Sửa</a>
                                    <?php if ($current_user['role'] == 'dentist'): ?>
                                        <a href="delete.php?id=<?php echo $patient['id']; ?>"
                                           class="btn btn-danger"
                                           style="padding: 8px 12px; font-size: 14px;"
                                           onclick="return confirm('⚠️ Bạn có chắc muốn xóa bệnh nhân này?\n\nTất cả lịch hẹn và hồ sơ khám bệnh sẽ bị xóa vĩnh viễn!')">🗑️ Xóa</a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Phân trang -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>">« Trước</a>
                <?php endif; ?>
                
                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>" 
                       class="<?php echo $i == $page ? 'active' : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>">Sau »</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 20px; color: #666;">
            Tổng cộng: <?php echo $total_records; ?> bệnh nhân
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.module-container').classList.add('fade-in');
        });
    </script>
</body>
</html>
