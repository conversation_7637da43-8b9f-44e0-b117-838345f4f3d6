<?php
/**
 * API Patients Endpoints
 * GET /api/patients - List patients with pagination and search
 * POST /api/patients - Create new patient
 * PUT /api/patients/{id} - Update patient
 * DELETE /api/patients/{id} - Delete patient
 */

require_once '../config/cors.php';
require_once '../config/response.php';
require_once '../config/middleware.php';
require_once '../../config/database.php';

$database = new Database();

// Authenticate user
$current_user = ApiMiddleware::authenticate();

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        
        case 'GET':
            // List patients with pagination and search
            $pagination = ApiMiddleware::validatePagination();
            $search = ApiMiddleware::parseSearch();
            
            // Build query
            $where_conditions = [];
            $params = [];
            
            if (!empty($search)) {
                $where_conditions[] = "(hoten LIKE ? OR sdt LIKE ? OR email LIKE ? OR cmnd LIKE ?)";
                $search_param = "%$search%";
                $params = [$search_param, $search_param, $search_param, $search_param];
            }
            
            $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
            
            // Count total records
            $count_sql = "SELECT COUNT(*) as total FROM benhnhan $where_clause";
            $total = $database->count($count_sql, $params);
            
            // Get patients
            $sql = "SELECT id, hoten, sdt, email, diachi, ngaysinh, gioitinh, cmnd, 
                           nghe_nghiep, trang_thai_dieu_tri, lan_kham_cuoi, created_at
                    FROM benhnhan 
                    $where_clause 
                    ORDER BY created_at DESC 
                    LIMIT {$pagination['limit']} OFFSET {$pagination['offset']}";
            
            $patients = $database->fetchAll($sql, $params);
            
            // Format dates
            foreach ($patients as &$patient) {
                $patient['ngaysinh'] = $patient['ngaysinh'] ? date('d/m/Y', strtotime($patient['ngaysinh'])) : null;
                $patient['lan_kham_cuoi'] = $patient['lan_kham_cuoi'] ? date('d/m/Y', strtotime($patient['lan_kham_cuoi'])) : null;
                $patient['created_at'] = date('d/m/Y H:i', strtotime($patient['created_at']));
            }
            
            ApiResponse::paginated(
                $patients, 
                $total, 
                $pagination['page'], 
                $pagination['limit'],
                'Lấy danh sách bệnh nhân thành công'
            );
            break;
            
        case 'POST':
            // Create new patient
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                ApiResponse::error('Invalid JSON input', 400);
            }
            
            // Validate required fields
            ApiResponse::validateRequired($input, ['hoten', 'sdt']);
            
            // Sanitize input
            $data = ApiResponse::sanitize($input);
            
            // Check if phone number exists
            $existing = $database->fetchOne(
                "SELECT id FROM benhnhan WHERE sdt = ?", 
                [$data['sdt']]
            );
            
            if ($existing) {
                ApiResponse::error('Số điện thoại đã tồn tại trong hệ thống', 400);
            }
            
            // Insert patient
            $sql = "INSERT INTO benhnhan (hoten, sdt, email, diachi, ngaysinh, gioitinh, cmnd, 
                                         nghe_nghiep, tien_su_benh, di_ung, lien_he_khan_cap, ghi_chu) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $data['hoten'],
                $data['sdt'],
                $data['email'] ?? null,
                $data['diachi'] ?? null,
                $data['ngaysinh'] ?? null,
                $data['gioitinh'] ?? 'Nam',
                $data['cmnd'] ?? null,
                $data['nghe_nghiep'] ?? null,
                $data['tien_su_benh'] ?? null,
                $data['di_ung'] ?? null,
                $data['lien_he_khan_cap'] ?? null,
                $data['ghi_chu'] ?? null
            ];
            
            if ($database->execute($sql, $params)) {
                $patient_id = $database->lastInsertId();
                
                // Get created patient
                $new_patient = $database->fetchOne(
                    "SELECT * FROM benhnhan WHERE id = ?", 
                    [$patient_id]
                );
                
                ApiResponse::success($new_patient, 'Thêm bệnh nhân thành công', 201);
            } else {
                ApiResponse::error('Lỗi khi thêm bệnh nhân', 500);
            }
            break;
            
        default:
            ApiResponse::error('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("API Patients Error: " . $e->getMessage());
    ApiResponse::error('Lỗi hệ thống', 500);
}
?>
