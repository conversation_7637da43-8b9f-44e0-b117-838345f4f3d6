<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$user_id = $current_user['id'];

$database = new Database();
$error = '';
$success = '';

// Lấy thông tin user hiện tại
$sql = "SELECT * FROM users WHERE id = ?";
$user = $database->fetchOne($sql, [$user_id]);

if (!$user) {
    header("Location: ../../dashboard.php?error=user_not_found");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'update_profile') {
        $full_name = trim($_POST['full_name']);
        $email = trim($_POST['email']);
        $phone = trim($_POST['phone']);
        
        // Validation
        if (empty($full_name)) {
            $error = "Vui lòng nhập họ tên!";
        } elseif (empty($email)) {
            $error = "Vui lòng nhập email!";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "Email không hợp lệ!";
        } else {
            try {
                // Kiểm tra trùng email (trừ user hiện tại)
                $check_sql = "SELECT id FROM users WHERE email = ? AND id != ?";
                $existing = $database->fetchOne($check_sql, [$email, $user_id]);
                
                if ($existing) {
                    $error = "Email đã được sử dụng bởi tài khoản khác!";
                } else {
                    // Cập nhật thông tin
                    $update_sql = "UPDATE users SET full_name = ?, email = ?, phone = ?, updated_at = NOW() WHERE id = ?";

                    if ($database->execute($update_sql, [$full_name, $email, $phone, $user_id])) {
                        $success = "Cập nhật thông tin thành công!";
                        // Lấy lại thông tin mới
                        $user = $database->fetchOne($sql, [$user_id]);
                        
                        // Cập nhật session
                        $_SESSION['full_name'] = $full_name;
                        $_SESSION['email'] = $email;
                    } else {
                        $error = "Lỗi khi cập nhật thông tin!";
                    }
                }
            } catch(Exception $e) {
                $error = "Lỗi hệ thống: " . $e->getMessage();
            }
        }
    } elseif ($action == 'change_password') {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        // Validation
        if (empty($current_password)) {
            $error = "Vui lòng nhập mật khẩu hiện tại!";
        } elseif (empty($new_password)) {
            $error = "Vui lòng nhập mật khẩu mới!";
        } elseif (strlen($new_password) < 6) {
            $error = "Mật khẩu mới phải có ít nhất 6 ký tự!";
        } elseif ($new_password !== $confirm_password) {
            $error = "Xác nhận mật khẩu không khớp!";
        } else {
            // Kiểm tra mật khẩu hiện tại
            if (password_verify($current_password, $user['password']) || $user['password'] === $current_password) {
                // Cập nhật mật khẩu mới
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $update_sql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
                
                if ($database->execute($update_sql, [$hashed_password, $user_id])) {
                    $success = "Đổi mật khẩu thành công!";
                } else {
                    $error = "Lỗi khi cập nhật mật khẩu!";
                }
            } else {
                $error = "Mật khẩu hiện tại không đúng!";
            }
        }
    }
}

// Thông tin bác sĩ đã tích hợp trong bảng users
$is_dentist = ($user['role'] == 'dentist');
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hồ sơ cá nhân - <?php echo htmlspecialchars($user['full_name']); ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .profile-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px auto;
            max-width: 900px;
        }
        
        .profile-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
            margin: 0 auto 20px;
        }
        
        .profile-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .tab-btn {
            padding: 15px 30px;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab-btn.active {
            color: #4CAF50;
            border-bottom-color: #4CAF50;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #4CAF50;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .profile-tabs {
                flex-direction: column;
            }
            
            .tab-btn {
                border-bottom: none;
                border-left: 3px solid transparent;
            }
            
            .tab-btn.active {
                border-left-color: #4CAF50;
                border-bottom-color: transparent;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="../../dashboard.php" class="btn btn-secondary back-btn">← Về Dashboard</a>
    
    <div class="profile-container fade-in">
        <div class="profile-header">
            <div class="profile-avatar">
                <?php 
                $role_icons = [
                    'admin' => '👑',
                    'dentist' => '👨‍⚕️',
                    'assistant' => '👩‍💼',
                    'receptionist' => '👩‍💻'
                ];
                echo $role_icons[$user['role']] ?? '👤';
                ?>
            </div>
            <h1><?php echo htmlspecialchars($user['full_name']); ?></h1>
            <p style="color: #666; font-size: 16px;">
                <?php 
                $role_names = [
                    'admin' => 'Quản trị viên',
                    'dentist' => 'Bác sĩ Nha khoa',
                    'assistant' => 'Trợ lý Nha khoa',
                    'receptionist' => 'Lễ tân'
                ];
                echo $role_names[$user['role']] ?? $user['role'];
                ?>
            </p>
        </div>
        
        <!-- Thông báo -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Tabs -->
        <div class="profile-tabs">
            <button class="tab-btn active" onclick="showTab('info')">📋 Thông tin</button>
            <button class="tab-btn" onclick="showTab('edit')">✏️ Chỉnh sửa</button>
            <button class="tab-btn" onclick="showTab('password')">🔒 Đổi mật khẩu</button>
            <?php if ($is_dentist): ?>
                <button class="tab-btn" onclick="showTab('clinic')">🏥 Thông tin Phòng khám</button>
            <?php endif; ?>
        </div>
        
        <!-- Tab Thông tin -->
        <div id="info" class="tab-content active">
            <div class="info-card">
                <h3 style="margin-bottom: 20px; color: #333;">📋 Thông tin cá nhân</h3>
                <div class="info-row">
                    <span class="info-label">Tên đăng nhập:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['username']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Họ và tên:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['full_name']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['email']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Số điện thoại:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['phone'] ?: 'Chưa cập nhật'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Vai trò:</span>
                    <span class="info-value"><?php echo $role_names[$user['role']] ?? $user['role']; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Ngày tạo:</span>
                    <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Cập nhật cuối:</span>
                    <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($user['updated_at'])); ?></span>
                </div>
            </div>
        </div>
        
        <!-- Tab Chỉnh sửa -->
        <div id="edit" class="tab-content">
            <form method="POST">
                <input type="hidden" name="action" value="update_profile">
                
                <div class="form-grid">
                    <div class="form-group">
                        <label for="full_name">Họ và tên *</label>
                        <input type="text" id="full_name" name="full_name" required 
                               value="<?php echo htmlspecialchars($user['full_name']); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required 
                               value="<?php echo htmlspecialchars($user['email']); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Số điện thoại</label>
                        <input type="tel" id="phone" name="phone" 
                               value="<?php echo htmlspecialchars($user['phone']); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label>Tên đăng nhập</label>
                        <input type="text" value="<?php echo htmlspecialchars($user['username']); ?>"
                               disabled style="background: #f8f9fa; color: #666;">
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary">💾 Cập nhật thông tin</button>
                </div>
            </form>
        </div>
        
        <!-- Tab Đổi mật khẩu -->
        <div id="password" class="tab-content">
            <form method="POST">
                <input type="hidden" name="action" value="change_password">
                
                <div class="form-group">
                    <label for="current_password">Mật khẩu hiện tại *</label>
                    <input type="password" id="current_password" name="current_password" required>
                </div>
                
                <div class="form-group">
                    <label for="new_password">Mật khẩu mới *</label>
                    <input type="password" id="new_password" name="new_password" required 
                           minlength="6" placeholder="Ít nhất 6 ký tự">
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">Xác nhận mật khẩu mới *</label>
                    <input type="password" id="confirm_password" name="confirm_password" required>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-warning">🔒 Đổi mật khẩu</button>
                </div>
            </form>
        </div>
        
        <!-- Tab Thông tin Phòng khám -->
        <?php if ($is_dentist): ?>
        <div id="clinic" class="tab-content">
            <div class="info-card">
                <h3 style="margin-bottom: 20px; color: #333;">🏥 Thông tin Phòng khám Nha khoa</h3>
                <div class="info-row">
                    <span class="info-label">Tên phòng khám:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['clinic_name'] ?: 'Chưa cập nhật'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Địa chỉ phòng khám:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['clinic_address'] ?: 'Chưa cập nhật'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Điện thoại phòng khám:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['clinic_phone'] ?: 'Chưa cập nhật'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Chuyên khoa:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['specialty'] ?: 'Nha khoa tổng quát'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Số chứng chỉ hành nghề:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['license_number'] ?: 'Chưa cập nhật'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Kinh nghiệm:</span>
                    <span class="info-value"><?php echo ($user['experience_years'] ?: 0); ?> năm</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Bằng cấp:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['education'] ?: 'Chưa cập nhật'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Giới thiệu:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['bio'] ?: 'Chưa cập nhật'); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Trạng thái:</span>
                    <span class="info-value">
                        <?php
                        $status_names = [
                            'active' => '✅ Hoạt động',
                            'inactive' => '⏸️ Tạm ngưng'
                        ];
                        echo $status_names[$user['status']] ?? $user['status'];
                        ?>
                    </span>
                </div>
                <?php if ($user['bio']): ?>
                <div style="margin-top: 20px;">
                    <strong>Mô tả chi tiết:</strong>
                    <p style="margin-top: 10px; font-style: italic; color: #666; line-height: 1.6;">
                        "<?php echo nl2br(htmlspecialchars($user['bio'])); ?>"
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.profile-container').classList.add('fade-in');
            
            // Password confirmation validation
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');
            
            function validatePassword() {
                if (newPassword.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('Mật khẩu xác nhận không khớp');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }
            
            if (newPassword && confirmPassword) {
                newPassword.addEventListener('change', validatePassword);
                confirmPassword.addEventListener('keyup', validatePassword);
            }
        });
        
        function showTab(tabName) {
            // Hide all tabs
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Remove active class from all buttons
            const buttons = document.querySelectorAll('.tab-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
