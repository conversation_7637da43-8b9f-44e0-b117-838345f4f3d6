<?php
/**
 * API Logout Endpoint
 * POST /api/auth/logout
 */

require_once '../config/cors.php';
require_once '../config/response.php';

// Only allow POST method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    ApiResponse::error('Method not allowed', 405);
}

try {
    // Clear session
    session_start();
    session_destroy();
    
    ApiResponse::success(null, 'Đăng xuất thành công');
    
} catch (Exception $e) {
    error_log("API Logout Error: " . $e->getMessage());
    ApiResponse::error('Lỗi hệ thống', 500);
}
?>
