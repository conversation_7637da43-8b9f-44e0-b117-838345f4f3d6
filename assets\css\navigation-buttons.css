/* ========================================
   NAVIGATION BUTTONS - BACK & HOME
   Nút điều hướng: Quay về & Trang chủ
======================================== */

.navigation-buttons {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    display: flex;
    gap: 10px;
    flex-direction: column;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    min-width: 120px;
    justify-content: center;
}

.nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: #333;
}

/* Back Button - Blue Theme */
.back-btn {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
    color: white !important;
    border: 2px solid #2196F3;
}

.back-btn:hover {
    background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
    color: white !important;
    border-color: #1976D2;
    transform: translateY(-2px) scale(1.05);
}

/* Home Button - Green Theme */
.home-btn {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white !important;
    border: 2px solid #4CAF50;
}

.home-btn:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    color: white !important;
    border-color: #45a049;
    transform: translateY(-2px) scale(1.05);
}

/* Icon styling */
.nav-btn .icon {
    font-size: 18px;
    display: flex;
    align-items: center;
}

.nav-btn .text {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navigation-buttons {
        top: 15px;
        left: 15px;
        gap: 8px;
    }
    
    .nav-btn {
        padding: 10px 14px;
        font-size: 13px;
        min-width: 100px;
    }
    
    .nav-btn .icon {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .navigation-buttons {
        top: 10px;
        left: 10px;
        flex-direction: row;
        gap: 5px;
    }
    
    .nav-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 80px;
        border-radius: 20px;
    }
    
    .nav-btn .text {
        display: none;
    }
    
    .nav-btn .icon {
        font-size: 18px;
    }
}

/* Animation Effects */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.navigation-buttons {
    animation: slideInLeft 0.5s ease-out;
}

.nav-btn:nth-child(1) {
    animation-delay: 0.1s;
}

.nav-btn:nth-child(2) {
    animation-delay: 0.2s;
}

/* Hover Effects */
.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
    border-radius: inherit;
}

.nav-btn:hover::before {
    transform: translateX(100%);
}

/* Focus States for Accessibility */
.nav-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

.back-btn:focus {
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
}

.home-btn:focus {
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.3);
}

/* Print Media - Hide navigation buttons when printing */
@media print {
    .navigation-buttons {
        display: none !important;
    }
}
