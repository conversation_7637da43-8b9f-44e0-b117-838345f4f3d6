<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$patient_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$error = '';
$success = '';

if (!$patient_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

$database = new Database();

// Lấy thông tin bệnh nhân hiện tại
$sql = "SELECT * FROM benhnhan WHERE id = ?";
$patient = $database->fetchOne($sql, [$patient_id]);

if (!$patient) {
    header("Location: list.php?error=not_found");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $hoten = trim($_POST['hoten']);
    $sdt = trim($_POST['sdt']);
    $email = trim($_POST['email']);
    $diachi = trim($_POST['diachi']);
    $ngaysinh = $_POST['ngaysinh'];
    $gioitinh = $_POST['gioitinh'];
    $cmnd = trim($_POST['cmnd']);
    $nghe_nghiep = trim($_POST['nghe_nghiep']);
    $tien_su_benh = trim($_POST['tien_su_benh']);
    $di_ung = trim($_POST['di_ung']);
    $lien_he_khan_cap = trim($_POST['lien_he_khan_cap']);
    $ghi_chu = trim($_POST['ghi_chu']);
    
    // Validation
    if (empty($hoten)) {
        $error = "Vui lòng nhập họ tên!";
    } elseif (empty($sdt)) {
        $error = "Vui lòng nhập số điện thoại!";
    } else {
        try {
            // Kiểm tra trùng số điện thoại (trừ bệnh nhân hiện tại)
            $check_sql = "SELECT id FROM benhnhan WHERE sdt = ? AND id != ?";
            $existing = $database->fetchOne($check_sql, [$sdt, $patient_id]);
            
            if ($existing) {
                $error = "Số điện thoại đã tồn tại trong hệ thống!";
            } else {
                // Cập nhật thông tin bệnh nhân
                $sql = "UPDATE benhnhan SET 
                        hoten = ?, sdt = ?, email = ?, diachi = ?, ngaysinh = ?, 
                        gioitinh = ?, cmnd = ?, nghe_nghiep = ?, tien_su_benh = ?, 
                        di_ung = ?, lien_he_khan_cap = ?, ghi_chu = ?, updated_at = NOW()
                        WHERE id = ?";
                
                $params = [$hoten, $sdt, $email, $diachi, $ngaysinh, $gioitinh, $cmnd, 
                          $nghe_nghiep, $tien_su_benh, $di_ung, $lien_he_khan_cap, $ghi_chu, $patient_id];
                
                if ($database->execute($sql, $params)) {
                    $success = "Cập nhật thông tin bệnh nhân thành công!";
                    // Lấy lại thông tin mới
                    $patient = $database->fetchOne("SELECT * FROM benhnhan WHERE id = ?", [$patient_id]);
                } else {
                    $error = "Lỗi khi cập nhật thông tin bệnh nhân!";
                }
            }
        } catch(Exception $e) {
            $error = "Lỗi hệ thống: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chỉnh sửa Bệnh nhân - <?php echo htmlspecialchars($patient['hoten']); ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/icons.css">
    <style>
        .form-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px auto;
            max-width: 800px;
        }
        
        .form-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.2);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="view.php?id=<?php echo $patient_id; ?>" class="btn btn-secondary back-btn">← Về thông tin</a>
    
    <div class="form-container fade-in">
        <div class="form-header">
            <h1>
                <img src="../../assets/gifs/patient-icon.gif" alt="Bệnh nhân" style="width: 40px; height: 40px; vertical-align: middle; margin-right: 10px;">
                Chỉnh sửa Bệnh nhân
            </h1>
            <p style="color: #666; margin-top: 10px;">ID: <?php echo $patient['id']; ?></p>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-grid">
                <div class="form-group">
                    <label for="hoten">Họ và tên *</label>
                    <input type="text" id="hoten" name="hoten" required 
                           value="<?php echo htmlspecialchars($patient['hoten']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="sdt">Số điện thoại *</label>
                    <input type="tel" id="sdt" name="sdt" required 
                           value="<?php echo htmlspecialchars($patient['sdt']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" 
                           value="<?php echo htmlspecialchars($patient['email']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="ngaysinh">Ngày sinh</label>
                    <input type="date" id="ngaysinh" name="ngaysinh" 
                           value="<?php echo htmlspecialchars($patient['ngaysinh']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="gioitinh">Giới tính</label>
                    <select id="gioitinh" name="gioitinh">
                        <option value="">-- Chọn giới tính --</option>
                        <option value="Nam" <?php echo ($patient['gioitinh'] == 'Nam') ? 'selected' : ''; ?>>Nam</option>
                        <option value="Nữ" <?php echo ($patient['gioitinh'] == 'Nữ') ? 'selected' : ''; ?>>Nữ</option>
                        <option value="Khác" <?php echo ($patient['gioitinh'] == 'Khác') ? 'selected' : ''; ?>>Khác</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="cmnd">CMND/CCCD</label>
                    <input type="text" id="cmnd" name="cmnd" 
                           value="<?php echo htmlspecialchars($patient['cmnd']); ?>">
                </div>
                
                <div class="form-group full-width">
                    <label for="diachi">Địa chỉ</label>
                    <input type="text" id="diachi" name="diachi" 
                           value="<?php echo htmlspecialchars($patient['diachi']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="nghe_nghiep">Nghề nghiệp</label>
                    <input type="text" id="nghe_nghiep" name="nghe_nghiep" 
                           value="<?php echo htmlspecialchars($patient['nghe_nghiep']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="lien_he_khan_cap">Liên hệ khẩn cấp</label>
                    <input type="text" id="lien_he_khan_cap" name="lien_he_khan_cap" 
                           placeholder="Tên và số điện thoại"
                           value="<?php echo htmlspecialchars($patient['lien_he_khan_cap']); ?>">
                </div>
                
                <div class="form-group full-width">
                    <label for="tien_su_benh">Tiền sử bệnh</label>
                    <textarea id="tien_su_benh" name="tien_su_benh" 
                              placeholder="Các bệnh đã mắc, phẫu thuật..."><?php echo htmlspecialchars($patient['tien_su_benh']); ?></textarea>
                </div>
                
                <div class="form-group full-width">
                    <label for="di_ung">Dị ứng</label>
                    <textarea id="di_ung" name="di_ung" 
                              placeholder="Dị ứng thuốc, thức ăn..."><?php echo htmlspecialchars($patient['di_ung']); ?></textarea>
                </div>
                
                <div class="form-group full-width">
                    <label for="ghi_chu">Ghi chú</label>
                    <textarea id="ghi_chu" name="ghi_chu" 
                              placeholder="Thông tin bổ sung..."><?php echo htmlspecialchars($patient['ghi_chu']); ?></textarea>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn btn-primary">
                    <div class="loading-icon" style="display: none;" id="loading"></div>
                    💾 Cập nhật thông tin
                </button>
                <a href="view.php?id=<?php echo $patient_id; ?>" class="btn btn-secondary">❌ Hủy</a>
                <a href="list.php" class="btn btn-warning">📋 Về danh sách</a>
            </div>
        </form>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.form-container').classList.add('fade-in');
            
            // Loading effect
            document.querySelector('form').addEventListener('submit', function() {
                document.getElementById('loading').style.display = 'inline-block';
            });
        });
    </script>
</body>
</html>
