/**
 * Authentication Module
 * Handles login, logout, and user session management
 */

class AuthManager {
    constructor() {
        this.currentUser = null;
        this.token = localStorage.getItem('api_token');
    }

    /**
     * Initialize authentication
     */
    async init() {
        if (this.token) {
            try {
                await this.verifyToken();
            } catch (error) {
                console.log('Token verification failed:', error);
                this.logout();
            }
        } else {
            this.showLoginPage();
        }
    }

    /**
     * Verify current token
     */
    async verifyToken() {
        try {
            const response = await api.verifyToken();
            if (response.success) {
                this.currentUser = response.data.user;
                this.showMainApp();
                return true;
            } else {
                throw new Error('Token verification failed');
            }
        } catch (error) {
            this.logout();
            throw error;
        }
    }

    /**
     * Login user
     */
    async login(username, password) {
        try {
            this.showLoading(true);
            
            const response = await api.login(username, password);
            
            if (response.success) {
                this.token = response.data.token;
                this.currentUser = response.data.user;
                
                // Store token
                api.setToken(this.token);
                
                // Show main application
                this.showMainApp();
                
                api.showSuccess('Đăng nhập thành công!');
                return true;
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            this.showLoginError(error.message);
            return false;
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Logout user
     */
    async logout() {
        try {
            if (this.token) {
                await api.logout();
            }
        } catch (error) {
            console.log('Logout API call failed:', error);
        } finally {
            // Clear local data
            this.token = null;
            this.currentUser = null;
            api.setToken(null);
            
            // Show login page
            this.showLoginPage();
        }
    }

    /**
     * Show login page
     */
    showLoginPage() {
        document.getElementById('loginPage').classList.remove('d-none');
        document.getElementById('mainApp').classList.add('d-none');
        
        // Setup login form
        this.setupLoginForm();
    }

    /**
     * Show main application
     */
    showMainApp() {
        document.getElementById('loginPage').classList.add('d-none');
        document.getElementById('mainApp').classList.remove('d-none');
        
        // Update user info in navbar
        if (this.currentUser) {
            document.getElementById('userName').textContent = this.currentUser.full_name;
        }
        
        // Setup navigation
        this.setupNavigation();
        
        // Load dashboard by default
        app.loadPage('dashboard');
    }

    /**
     * Setup login form
     */
    setupLoginForm() {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value;
                
                if (!username || !password) {
                    this.showLoginError('Vui lòng nhập đầy đủ thông tin');
                    return;
                }
                
                await this.login(username, password);
            });
        }
    }

    /**
     * Setup navigation
     */
    setupNavigation() {
        // Navigation links
        document.querySelectorAll('[data-page]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = e.currentTarget.getAttribute('data-page');
                app.loadPage(page);
                
                // Update active nav item
                document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // Logout button
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        }
    }

    /**
     * Show loading state
     */
    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (show) {
            overlay.classList.remove('d-none');
        } else {
            overlay.classList.add('d-none');
        }
    }

    /**
     * Show login error
     */
    showLoginError(message) {
        const errorDiv = document.getElementById('loginError');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.classList.remove('d-none');
            
            // Hide after 5 seconds
            setTimeout(() => {
                errorDiv.classList.add('d-none');
            }, 5000);
        }
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * Check if user has role
     */
    hasRole(role) {
        return this.currentUser && this.currentUser.role === role;
    }

    /**
     * Check if user is dentist (admin)
     */
    isDentist() {
        return this.hasRole('dentist');
    }

    /**
     * Check if user is assistant
     */
    isAssistant() {
        return this.hasRole('assistant');
    }
}

// Create global auth manager instance
window.auth = new AuthManager();
