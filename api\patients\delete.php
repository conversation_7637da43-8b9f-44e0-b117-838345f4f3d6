<?php
/**
 * API Delete Patient
 * DELETE /api/patients/{id}
 */

require_once '../config/cors.php';
require_once '../config/response.php';
require_once '../config/middleware.php';
require_once '../../config/database.php';

// Only allow DELETE method
if ($_SERVER['REQUEST_METHOD'] !== 'DELETE') {
    ApiResponse::error('Method not allowed', 405);
}

$database = new Database();

// Authenticate user and require dentist role
$current_user = ApiMiddleware::authenticate();
ApiMiddleware::requireDentist($current_user);

try {
    // Get patient ID from URL
    $patient_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    if (!$patient_id) {
        ApiResponse::error('ID bệnh nhân không hợp lệ', 400);
    }
    
    // Check if patient exists
    $patient = $database->fetchOne(
        "SELECT * FROM benhnhan WHERE id = ?", 
        [$patient_id]
    );
    
    if (!$patient) {
        ApiResponse::error('Bệnh nhân không tồn tại', 404);
    }
    
    // Begin transaction
    $database->beginTransaction();
    
    try {
        // Delete related records first (due to foreign key constraints)
        // Delete medical records
        $database->execute("DELETE FROM medical_records WHERE patient_id = ?", [$patient_id]);
        
        // Delete appointments
        $database->execute("DELETE FROM appointments WHERE patient_id = ?", [$patient_id]);
        
        // Finally delete patient
        $result = $database->execute("DELETE FROM benhnhan WHERE id = ?", [$patient_id]);
        
        if ($result) {
            $database->commit();
            ApiResponse::success(null, 'Xóa bệnh nhân thành công');
        } else {
            $database->rollback();
            ApiResponse::error('Lỗi khi xóa bệnh nhân', 500);
        }
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("API Delete Patient Error: " . $e->getMessage());
    ApiResponse::error('Lỗi hệ thống', 500);
}
?>
