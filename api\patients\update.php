<?php
/**
 * API Update Patient
 * PUT /api/patients/{id}
 */

require_once '../config/cors.php';
require_once '../config/response.php';
require_once '../config/middleware.php';
require_once '../../config/database.php';

// Only allow PUT method
if ($_SERVER['REQUEST_METHOD'] !== 'PUT') {
    ApiResponse::error('Method not allowed', 405);
}

$database = new Database();

// Authenticate user
$current_user = ApiMiddleware::authenticate();

try {
    // Get patient ID from URL
    $patient_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    if (!$patient_id) {
        ApiResponse::error('ID bệnh nhân không hợp lệ', 400);
    }
    
    // Check if patient exists
    $existing_patient = $database->fetchOne(
        "SELECT * FROM benhnhan WHERE id = ?", 
        [$patient_id]
    );
    
    if (!$existing_patient) {
        ApiResponse::error('<PERSON>ệnh nhân không tồn tại', 404);
    }
    
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        ApiResponse::error('Invalid JSON input', 400);
    }
    
    // Validate required fields
    ApiResponse::validateRequired($input, ['hoten', 'sdt']);
    
    // Sanitize input
    $data = ApiResponse::sanitize($input);
    
    // Check if phone number exists for other patients
    $phone_check = $database->fetchOne(
        "SELECT id FROM benhnhan WHERE sdt = ? AND id != ?", 
        [$data['sdt'], $patient_id]
    );
    
    if ($phone_check) {
        ApiResponse::error('Số điện thoại đã được sử dụng bởi bệnh nhân khác', 400);
    }
    
    // Update patient
    $sql = "UPDATE benhnhan SET 
                hoten = ?, sdt = ?, email = ?, diachi = ?, ngaysinh = ?, 
                gioitinh = ?, cmnd = ?, nghe_nghiep = ?, tien_su_benh = ?, 
                di_ung = ?, lien_he_khan_cap = ?, ghi_chu = ?, 
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";
    
    $params = [
        $data['hoten'],
        $data['sdt'],
        $data['email'] ?? null,
        $data['diachi'] ?? null,
        $data['ngaysinh'] ?? null,
        $data['gioitinh'] ?? 'Nam',
        $data['cmnd'] ?? null,
        $data['nghe_nghiep'] ?? null,
        $data['tien_su_benh'] ?? null,
        $data['di_ung'] ?? null,
        $data['lien_he_khan_cap'] ?? null,
        $data['ghi_chu'] ?? null,
        $patient_id
    ];
    
    if ($database->execute($sql, $params)) {
        // Get updated patient
        $updated_patient = $database->fetchOne(
            "SELECT * FROM benhnhan WHERE id = ?", 
            [$patient_id]
        );
        
        ApiResponse::success($updated_patient, 'Cập nhật bệnh nhân thành công');
    } else {
        ApiResponse::error('Lỗi khi cập nhật bệnh nhân', 500);
    }
    
} catch (Exception $e) {
    error_log("API Update Patient Error: " . $e->getMessage());
    ApiResponse::error('Lỗi hệ thống', 500);
}
?>
