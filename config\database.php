<?php
/**
 * <PERSON><PERSON><PERSON> hình kết nối cơ sở dữ liệu
 * Phòng khám Management System
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'phongkham';
    private $username = 'root';
    private $password = '';
    private $conn;
    private static $instance = null;
    private $inTransaction = false;

    /**
     * Kết nối cơ sở dữ liệu
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "Lỗi kết nối: " . $exception->getMessage();
        }
        
        return $this->conn;
    }

    /**
     * Đóng kết nối
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * Kiểm tra kết nối
     */
    public function testConnection() {
        $conn = $this->getConnection();
        if ($conn) {
            return true;
        }
        return false;
    }

    /**
     * Thực thi câu lệnh SQL
     */
    public function execute($sql, $params = []) {
        try {
            $conn = $this->getConnection();
            if (!$conn) {
                error_log("Database connection failed in execute()");
                return false;
            }

            $stmt = $conn->prepare($sql);
            if (!$stmt) {
                error_log("Failed to prepare statement: " . $sql);
                return false;
            }

            $result = $stmt->execute($params);
            if (!$result) {
                $errorInfo = $stmt->errorInfo();
                error_log("Execute failed: " . print_r($errorInfo, true));
                error_log("SQL: " . $sql);
                error_log("Params: " . print_r($params, true));
            }

            return $result;
        } catch(PDOException $e) {
            error_log("Database Error in execute(): " . $e->getMessage());
            error_log("SQL: " . $sql);
            error_log("Params: " . print_r($params, true));
            return false;
        }
    }

    /**
     * Lấy một bản ghi
     */
    public function fetchOne($sql, $params = []) {
        try {
            $conn = $this->getConnection();
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch(PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Lấy nhiều bản ghi
     */
    public function fetchAll($sql, $params = []) {
        try {
            $conn = $this->getConnection();
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch(PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Lấy ID của bản ghi vừa thêm
     */
    public function lastInsertId() {
        $conn = $this->getConnection();
        return $conn->lastInsertId();
    }

    /**
     * Lấy ID của bản ghi vừa thêm (alias)
     */
    public function getLastInsertId() {
        return $this->lastInsertId();
    }

    /**
     * Đếm số bản ghi
     */
    public function count($sql, $params = []) {
        try {
            $conn = $this->getConnection();
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch(PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Bắt đầu transaction
     */
    public function beginTransaction() {
        try {
            $conn = $this->getConnection();
            if ($conn && !$conn->inTransaction()) {
                $result = $conn->beginTransaction();
                $this->inTransaction = $result;
                return $result;
            }
            return true; // Already in transaction
        } catch (Exception $e) {
            error_log("Begin transaction error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Commit transaction
     */
    public function commit() {
        try {
            $conn = $this->getConnection();
            if ($conn && $conn->inTransaction()) {
                $result = $conn->commit();
                $this->inTransaction = false;
                return $result;
            }
            return false;
        } catch (Exception $e) {
            error_log("Commit transaction error: " . $e->getMessage());
            $this->inTransaction = false;
            return false;
        }
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        try {
            $conn = $this->getConnection();
            if ($conn && $conn->inTransaction()) {
                $result = $conn->rollback();
                $this->inTransaction = false;
                return $result;
            }
            $this->inTransaction = false; // Reset flag even if no active transaction
            return true; // Don't return false to avoid errors
        } catch (Exception $e) {
            error_log("Rollback transaction error: " . $e->getMessage());
            $this->inTransaction = false;
            return true; // Don't return false to avoid errors
        }
    }

    /**
     * Kiểm tra có đang trong transaction không
     */
    public function inTransaction() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return $conn->inTransaction();
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }
}

// Tạo instance global
$database = new Database();
$db = $database->getConnection();
?>
