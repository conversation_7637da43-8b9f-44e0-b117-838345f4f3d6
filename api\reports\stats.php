<?php
/**
 * API Reports and Statistics
 * GET /api/reports/stats
 */

require_once '../config/cors.php';
require_once '../config/response.php';
require_once '../config/middleware.php';
require_once '../../config/database.php';

// Only allow GET method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    ApiResponse::error('Method not allowed', 405);
}

$database = new Database();

// Authenticate user
$current_user = ApiMiddleware::authenticate();

try {
    // Get date range (default to current month)
    $date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01');
    $date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-t');
    
    // Basic statistics
    $stats = [];
    
    // Total patients
    $stats['total_patients'] = $database->count("SELECT COUNT(*) as total FROM benhnhan", []);
    
    // Today's appointments
    $stats['today_appointments'] = $database->count(
        "SELECT COUNT(*) as total FROM appointments WHERE appointment_date = CURDATE()", 
        []
    );
    
    // Completed appointments today
    $stats['completed_today'] = $database->count(
        "SELECT COUNT(*) as total FROM appointments WHERE appointment_date = CURDATE() AND status = 'hoan_thanh'", 
        []
    );
    
    // Revenue for date range
    $revenue_result = $database->fetchOne(
        "SELECT SUM(actual_cost) as total_revenue 
         FROM appointments 
         WHERE appointment_date BETWEEN ? AND ? AND payment_status = 'da_thanh_toan'",
        [$date_from, $date_to]
    );
    $stats['revenue'] = $revenue_result['total_revenue'] ?? 0;
    
    // Appointments by status for date range
    $status_stats = $database->fetchAll(
        "SELECT status, COUNT(*) as count 
         FROM appointments 
         WHERE appointment_date BETWEEN ? AND ? 
         GROUP BY status",
        [$date_from, $date_to]
    );
    
    $stats['appointments_by_status'] = [];
    foreach ($status_stats as $status) {
        $stats['appointments_by_status'][$status['status']] = (int)$status['count'];
    }
    
    // Service types statistics
    $service_stats = $database->fetchAll(
        "SELECT service_type, COUNT(*) as count 
         FROM appointments 
         WHERE appointment_date BETWEEN ? AND ? 
         GROUP BY service_type 
         ORDER BY count DESC",
        [$date_from, $date_to]
    );
    
    $stats['services'] = $service_stats;
    
    // Monthly revenue trend (last 6 months)
    $monthly_revenue = $database->fetchAll(
        "SELECT 
            DATE_FORMAT(appointment_date, '%Y-%m') as month,
            SUM(actual_cost) as revenue,
            COUNT(*) as appointments
         FROM appointments 
         WHERE appointment_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
         AND payment_status = 'da_thanh_toan'
         GROUP BY DATE_FORMAT(appointment_date, '%Y-%m')
         ORDER BY month DESC",
        []
    );
    
    $stats['monthly_trend'] = $monthly_revenue;
    
    // Recent patients (last 10)
    $recent_patients = $database->fetchAll(
        "SELECT id, hoten, sdt, created_at 
         FROM benhnhan 
         ORDER BY created_at DESC 
         LIMIT 10",
        []
    );
    
    foreach ($recent_patients as &$patient) {
        $patient['created_at'] = date('d/m/Y H:i', strtotime($patient['created_at']));
    }
    
    $stats['recent_patients'] = $recent_patients;
    
    // Upcoming appointments (next 7 days)
    $upcoming = $database->fetchAll(
        "SELECT a.id, a.appointment_date, a.appointment_time, a.service_type,
                b.hoten as patient_name, b.sdt as patient_phone
         FROM appointments a
         LEFT JOIN benhnhan b ON a.patient_id = b.id
         WHERE a.appointment_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
         AND a.status IN ('dat_lich', 'xac_nhan')
         ORDER BY a.appointment_date, a.appointment_time
         LIMIT 10",
        []
    );
    
    foreach ($upcoming as &$appointment) {
        $appointment['appointment_date'] = date('d/m/Y', strtotime($appointment['appointment_date']));
        $appointment['appointment_time'] = date('H:i', strtotime($appointment['appointment_time']));
    }
    
    $stats['upcoming_appointments'] = $upcoming;
    
    ApiResponse::success($stats, 'Lấy thống kê thành công');
    
} catch (Exception $e) {
    error_log("API Reports Error: " . $e->getMessage());
    ApiResponse::error('Lỗi hệ thống', 500);
}
?>
