<?php
/**
 * API Token Verification
 * GET /api/auth/verify
 */

require_once '../config/cors.php';
require_once '../config/response.php';
require_once '../../config/database.php';

// Only allow GET method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    ApiResponse::error('Method not allowed', 405);
}

try {
    // Get token from header or query parameter
    $token = null;
    
    // Check Authorization header
    $headers = getallheaders();
    if (isset($headers['Authorization'])) {
        $auth_header = $headers['Authorization'];
        if (strpos($auth_header, 'Bearer ') === 0) {
            $token = substr($auth_header, 7);
        }
    }
    
    // Check query parameter as fallback
    if (!$token && isset($_GET['token'])) {
        $token = $_GET['token'];
    }
    
    if (!$token) {
        ApiResponse::error('Token không được cung cấp', 401);
    }
    
    // Verify token
    session_start();
    if (!isset($_SESSION['api_token']) || $_SESSION['api_token'] !== $token) {
        ApiResponse::error('Token không hợp lệ', 401);
    }
    
    // Get user info
    $database = new Database();
    $user = $database->fetchOne(
        "SELECT id, username, full_name, role, email, clinic_name FROM users WHERE id = ?",
        [$_SESSION['api_user_id']]
    );
    
    if (!$user) {
        ApiResponse::error('Người dùng không tồn tại', 401);
    }
    
    ApiResponse::success([
        'user' => $user,
        'token_valid' => true
    ], 'Token hợp lệ');
    
} catch (Exception $e) {
    error_log("API Verify Error: " . $e->getMessage());
    ApiResponse::error('Lỗi hệ thống', 500);
}
?>
