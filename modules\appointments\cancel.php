<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$appointment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$appointment_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

$database = new Database();

// Lấy thông tin lịch hẹn với database mới
$sql = "SELECT a.*,
               b.hoten as patient_name,
               b.sdt as patient_phone
        FROM appointments a
        LEFT JOIN benhnhan b ON a.patient_id = b.id
        WHERE a.id = ?";

$appointment = $database->fetchOne($sql, [$appointment_id]);

if (!$appointment) {
    header("Location: list.php?error=not_found");
    exit();
}

// <PERSON><PERSON><PERSON> sĩ tư nhân có thể hủy tất cả lịch hẹn

// Kiểm tra trạng thái có thể hủy
if (in_array($appointment['status'], ['hoan_thanh', 'huy_lich'])) {
    header("Location: view.php?id=$appointment_id&error=cannot_cancel");
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['confirm_cancel']) && $_POST['confirm_cancel'] == 'yes') {
        $cancel_reason = trim($_POST['cancel_reason']);
        
        try {
            // Cập nhật trạng thái lịch hẹn
            $update_sql = "UPDATE appointments SET
                          status = 'huy_lich',
                          notes = CONCAT(COALESCE(notes, ''), '\n\n[HỦY LỊCH HẸN]\nLý do: ', ?),
                          updated_at = NOW()
                          WHERE id = ?";
            
            if ($database->execute($update_sql, [$cancel_reason, $appointment_id])) {
                header("Location: list.php?success=cancelled");
                exit();
            } else {
                $error = "Không thể hủy lịch hẹn!";
            }
            
        } catch(Exception $e) {
            $error = "Lỗi hệ thống: " . $e->getMessage();
        }
    } else {
        header("Location: view.php?id=$appointment_id");
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hủy Lịch hẹn - LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .cancel-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px auto;
            max-width: 600px;
            text-align: center;
        }
        
        .cancel-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .warning-icon {
            font-size: 80px;
            color: #ff6b6b;
            margin-bottom: 20px;
        }
        
        .appointment-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #ff6b6b;
            text-align: left;
        }
        
        .appointment-info h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .warning-text {
            color: #721c24;
            background: #f8d7da;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="view.php?id=<?php echo $appointment_id; ?>" class="btn btn-secondary back-btn">← Về chi tiết</a>
    
    <div class="cancel-container fade-in">
        <div class="cancel-header">
            <div class="warning-icon">⚠️</div>
            <h1 style="color: #ff6b6b;">Xác nhận hủy Lịch hẹn</h1>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <div class="appointment-info">
            <h3>Thông tin lịch hẹn sẽ bị hủy:</h3>
            <div class="info-item">
                <span class="info-label">Mã lịch hẹn:</span>
                <span class="info-value">LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Bệnh nhân:</span>
                <span class="info-value"><?php echo htmlspecialchars($appointment['patient_name']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Số điện thoại:</span>
                <span class="info-value"><?php echo htmlspecialchars($appointment['patient_phone']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Bác sĩ:</span>
                <span class="info-value">BS. Nguyễn Văn Nam (Bác sĩ tư nhân)</span>
            </div>
            <div class="info-item">
                <span class="info-label">Dịch vụ:</span>
                <span class="info-value">
                    <?php
                    $service_labels = [
                        'kham_tong_quat' => '🔍 Khám tổng quát',
                        'tay_trang' => '✨ Tẩy trắng',
                        'nho_rang' => '🦷 Nhổ răng',
                        'han_tram' => '🔧 Hàn trám',
                        'dieu_tri_tuy' => '🩺 Điều trị tủy',
                        'nieng_rang' => '📐 Niềng răng',
                        'cay_ghep' => '🔩 Cấy ghép',
                        'phau_thuat' => '⚕️ Phẫu thuật',
                        'tu_van' => '💬 Tư vấn'
                    ];
                    echo $service_labels[$appointment['service_type']] ?? '🔍 Khám tổng quát';
                    ?>
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">Ngày giờ:</span>
                <span class="info-value">
                    <?php echo date('d/m/Y H:i', strtotime($appointment['appointment_date'] . ' ' . $appointment['appointment_time'])); ?>
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">Trạng thái hiện tại:</span>
                <span class="info-value">
                    <?php
                    $status_names = [
                        'dat_lich' => '📅 Đã đặt',
                        'xac_nhan' => '✅ Xác nhận',
                        'dang_kham' => '🔄 Đang khám'
                    ];
                    echo $status_names[$appointment['status']] ?? $appointment['status'];
                    ?>
                </span>
            </div>
        </div>
        
        <div class="warning-text">
            <strong>⚠️ CẢNH BÁO:</strong><br>
            Hành động này sẽ hủy lịch hẹn và không thể hoàn tác!<br>
            Bệnh nhân sẽ cần đặt lịch mới nếu muốn khám.
        </div>
        
        <form method="POST" onsubmit="return confirmCancel()">
            <div class="form-group">
                <label for="cancel_reason">Lý do hủy lịch hẹn *</label>
                <textarea id="cancel_reason" name="cancel_reason" required 
                          placeholder="Nhập lý do hủy lịch hẹn (bắt buộc)..."></textarea>
            </div>
            
            <p style="margin-bottom: 30px; font-size: 18px; color: #333;">
                Bạn có chắc chắn muốn hủy lịch hẹn này không?
            </p>
            
            <button type="submit" name="confirm_cancel" value="yes" class="btn btn-danger">
                ❌ Xác nhận hủy
            </button>
            <a href="view.php?id=<?php echo $appointment_id; ?>" class="btn btn-secondary">
                🔙 Quay lại
            </a>
        </form>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.cancel-container').classList.add('fade-in');
        });
        
        function confirmCancel() {
            const reason = document.getElementById('cancel_reason').value.trim();
            if (!reason) {
                alert('Vui lòng nhập lý do hủy lịch hẹn!');
                return false;
            }
            
            return confirm('BẠN CÓ CHẮC CHẮN MUỐN HỦY LỊCH HẸN NÀY?\n\nHành động này không thể hoàn tác!');
        }
    </script>
</body>
</html>
