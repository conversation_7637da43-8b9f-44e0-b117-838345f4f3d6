<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';
require_once '../../includes/navigation-buttons.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

// Kiểm tra quyền admin
if ($current_user['role'] !== 'dentist') {
    header("Location: ../../dashboard.php?error=access_denied");
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $email = trim($_POST['email']);
    $full_name = trim($_POST['full_name']);
    $role = $_POST['role'];
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $clinic_name = trim($_POST['clinic_name']);
    $clinic_address = trim($_POST['clinic_address']);
    $clinic_phone = trim($_POST['clinic_phone']);
    $license_number = trim($_POST['license_number']);
    $specialty = trim($_POST['specialty']);
    $experience_years = (int)($_POST['experience_years'] ?? 1);
    $education = trim($_POST['education']);
    $bio = trim($_POST['bio']);
    
    // Validation
    if (empty($username) || empty($password) || empty($full_name) || empty($role)) {
        $error = "Vui lòng điền đầy đủ thông tin bắt buộc!";
    } elseif (strlen($username) < 3) {
        $error = "Username phải có ít nhất 3 ký tự!";
    } elseif (strlen($password) < 6) {
        $error = "Mật khẩu phải có ít nhất 6 ký tự!";
    } elseif ($password !== $confirm_password) {
        $error = "Mật khẩu xác nhận không khớp!";
    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Email không hợp lệ!";
    } else {
        try {
            // Kiểm tra username trùng
            $check_username = $database->fetchOne("SELECT id FROM users WHERE username = ?", [$username]);
            if ($check_username) {
                $error = "Username đã tồn tại!";
            } else {
                // Kiểm tra email trùng (nếu có)
                if (!empty($email)) {
                    $check_email = $database->fetchOne("SELECT id FROM users WHERE email = ?", [$email]);
                    if ($check_email) {
                        $error = "Email đã được sử dụng!";
                    }
                }
                
                if (empty($error)) {
                    // Hash password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Insert user
                    $sql = "INSERT INTO users (
                        username, password, email, full_name, role, phone, address,
                        clinic_name, clinic_address, clinic_phone, license_number,
                        specialty, experience_years, education, bio, status, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())";
                    
                    $params = [
                        $username, $hashed_password, $email, $full_name, $role, $phone, $address,
                        $clinic_name, $clinic_address, $clinic_phone, $license_number,
                        $specialty, $experience_years, $education, $bio
                    ];
                    
                    if ($database->execute($sql, $params)) {
                        $success = "Thêm người dùng thành công!";
                        // Reset form
                        $_POST = [];
                    } else {
                        $error = "Lỗi khi thêm người dùng!";
                    }
                }
            }
        } catch(Exception $e) {
            $error = "Lỗi hệ thống: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>➕ Thêm Người dùng</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/navigation-buttons.css">
    <style>
        .add-user-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 5px solid #4CAF50;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .form-row.single {
            grid-template-columns: 1fr;
        }
        
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }
        
        .strength-weak { color: #f44336; }
        .strength-medium { color: #ff9800; }
        .strength-strong { color: #4caf50; }
        
        .role-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .role-card {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .role-card:hover {
            border-color: #4CAF50;
            background: #f8f9fa;
        }
        
        .role-card.selected {
            border-color: #4CAF50;
            background: #e8f5e8;
        }
        
        .role-card input[type="radio"] {
            display: none;
        }
        
        .role-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }
        
        .role-name {
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .role-desc {
            font-size: 12px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .role-cards {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <?php renderNavigationButtons('list.php'); ?>

    <div class="add-user-container fade-in">
        <!-- Thông báo -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <div class="module-header">
            <h1>
                <span style="font-size: 40px;">➕</span>
                Thêm Người dùng Mới
            </h1>
            <p style="color: #666;">Tạo tài khoản mới cho nhân viên phòng khám</p>
        </div>

        <form method="POST" id="addUserForm">
            <!-- Thông tin đăng nhập -->
            <div class="form-section">
                <div class="section-title">
                    <span>🔐</span>
                    Thông tin Đăng nhập
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">Username *</label>
                        <input type="text" id="username" name="username" required 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                               placeholder="Tên đăng nhập (ít nhất 3 ký tự)">
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               placeholder="<EMAIL>">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">Mật khẩu *</label>
                        <input type="password" id="password" name="password" required 
                               placeholder="Mật khẩu (ít nhất 6 ký tự)">
                        <div id="passwordStrength" class="password-strength"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">Xác nhận mật khẩu *</label>
                        <input type="password" id="confirm_password" name="confirm_password" required 
                               placeholder="Nhập lại mật khẩu">
                        <div id="passwordMatch" class="password-strength"></div>
                    </div>
                </div>
            </div>

            <!-- Thông tin cá nhân -->
            <div class="form-section">
                <div class="section-title">
                    <span>👤</span>
                    Thông tin Cá nhân
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="full_name">Họ và tên *</label>
                        <input type="text" id="full_name" name="full_name" required 
                               value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                               placeholder="Họ và tên đầy đủ">
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Số điện thoại</label>
                        <input type="tel" id="phone" name="phone" 
                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                               placeholder="0901234567">
                    </div>
                </div>
                
                <div class="form-row single">
                    <div class="form-group">
                        <label for="address">Địa chỉ</label>
                        <textarea id="address" name="address" rows="2" 
                                  placeholder="Địa chỉ liên hệ"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Vai trò -->
            <div class="form-section">
                <div class="section-title">
                    <span>🎭</span>
                    Vai trò trong Hệ thống
                </div>
                
                <div class="role-cards">
                    <label class="role-card" for="role_dentist">
                        <input type="radio" id="role_dentist" name="role" value="dentist" 
                               <?php echo (($_POST['role'] ?? '') == 'dentist') ? 'checked' : ''; ?>>
                        <div class="role-icon">👨‍⚕️</div>
                        <div class="role-name">Bác sĩ Nha khoa</div>
                        <div class="role-desc">Quyền quản lý toàn bộ hệ thống</div>
                    </label>
                    
                    <label class="role-card" for="role_assistant">
                        <input type="radio" id="role_assistant" name="role" value="assistant" 
                               <?php echo (($_POST['role'] ?? '') == 'assistant') ? 'checked' : ''; ?>>
                        <div class="role-icon">👩‍💼</div>
                        <div class="role-name">Trợ lý Nha khoa</div>
                        <div class="role-desc">Hỗ trợ quản lý lịch hẹn và bệnh nhân</div>
                    </label>
                </div>
            </div>

            <!-- Thông tin chuyên môn (chỉ hiển thị khi chọn dentist) -->
            <div class="form-section" id="professionalInfo" style="display: none;">
                <div class="section-title">
                    <span>🏥</span>
                    Thông tin Chuyên môn
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="clinic_name">Tên phòng khám</label>
                        <input type="text" id="clinic_name" name="clinic_name" 
                               value="<?php echo htmlspecialchars($_POST['clinic_name'] ?? 'Phòng khám nha khoa tư nhân'); ?>"
                               placeholder="Tên phòng khám">
                    </div>
                    
                    <div class="form-group">
                        <label for="clinic_phone">SĐT phòng khám</label>
                        <input type="tel" id="clinic_phone" name="clinic_phone" 
                               value="<?php echo htmlspecialchars($_POST['clinic_phone'] ?? ''); ?>"
                               placeholder="028-1234-5678">
                    </div>
                </div>
                
                <div class="form-row single">
                    <div class="form-group">
                        <label for="clinic_address">Địa chỉ phòng khám</label>
                        <textarea id="clinic_address" name="clinic_address" rows="2" 
                                  placeholder="Địa chỉ phòng khám"><?php echo htmlspecialchars($_POST['clinic_address'] ?? ''); ?></textarea>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="license_number">Số chứng chỉ hành nghề</label>
                        <input type="text" id="license_number" name="license_number" 
                               value="<?php echo htmlspecialchars($_POST['license_number'] ?? ''); ?>"
                               placeholder="NK001234">
                    </div>
                    
                    <div class="form-group">
                        <label for="specialty">Chuyên khoa</label>
                        <input type="text" id="specialty" name="specialty" 
                               value="<?php echo htmlspecialchars($_POST['specialty'] ?? 'Nha khoa tổng quát'); ?>"
                               placeholder="Nha khoa tổng quát">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="experience_years">Số năm kinh nghiệm</label>
                        <input type="number" id="experience_years" name="experience_years" min="0" max="50"
                               value="<?php echo htmlspecialchars($_POST['experience_years'] ?? '1'); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="education">Trình độ học vấn</label>
                        <input type="text" id="education" name="education" 
                               value="<?php echo htmlspecialchars($_POST['education'] ?? ''); ?>"
                               placeholder="Bác sĩ Răng Hàm Mặt - Đại học Y Dược">
                    </div>
                </div>
                
                <div class="form-row single">
                    <div class="form-group">
                        <label for="bio">Giới thiệu</label>
                        <textarea id="bio" name="bio" rows="3" 
                                  placeholder="Mô tả ngắn về kinh nghiệm và chuyên môn"><?php echo htmlspecialchars($_POST['bio'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn btn-primary" style="padding: 15px 30px; font-size: 16px;">
                    💾 Tạo tài khoản
                </button>
                <a href="list.php" class="btn btn-secondary" style="padding: 15px 30px; font-size: 16px; margin-left: 10px;">
                    ← Quay lại danh sách
                </a>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.add-user-container').classList.add('fade-in');
            
            // Role selection handling
            const roleCards = document.querySelectorAll('.role-card');
            const professionalInfo = document.getElementById('professionalInfo');
            
            roleCards.forEach(card => {
                card.addEventListener('click', function() {
                    roleCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    
                    const role = this.querySelector('input[type="radio"]').value;
                    if (role === 'dentist') {
                        professionalInfo.style.display = 'block';
                    } else {
                        professionalInfo.style.display = 'none';
                    }
                });
            });
            
            // Initialize role selection
            const checkedRole = document.querySelector('input[name="role"]:checked');
            if (checkedRole) {
                checkedRole.closest('.role-card').classList.add('selected');
                if (checkedRole.value === 'dentist') {
                    professionalInfo.style.display = 'block';
                }
            }
            
            // Password strength checker
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const strengthDiv = document.getElementById('passwordStrength');
            const matchDiv = document.getElementById('passwordMatch');
            
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;
                let message = '';
                
                if (password.length >= 6) strength++;
                if (password.match(/[a-z]/)) strength++;
                if (password.match(/[A-Z]/)) strength++;
                if (password.match(/[0-9]/)) strength++;
                if (password.match(/[^a-zA-Z0-9]/)) strength++;
                
                if (password.length === 0) {
                    message = '';
                } else if (strength < 2) {
                    message = '❌ Mật khẩu yếu';
                    strengthDiv.className = 'password-strength strength-weak';
                } else if (strength < 4) {
                    message = '⚠️ Mật khẩu trung bình';
                    strengthDiv.className = 'password-strength strength-medium';
                } else {
                    message = '✅ Mật khẩu mạnh';
                    strengthDiv.className = 'password-strength strength-strong';
                }
                
                strengthDiv.textContent = message;
                checkPasswordMatch();
            });
            
            confirmPasswordInput.addEventListener('input', checkPasswordMatch);
            
            function checkPasswordMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                if (confirmPassword.length === 0) {
                    matchDiv.textContent = '';
                } else if (password === confirmPassword) {
                    matchDiv.textContent = '✅ Mật khẩu khớp';
                    matchDiv.className = 'password-strength strength-strong';
                } else {
                    matchDiv.textContent = '❌ Mật khẩu không khớp';
                    matchDiv.className = 'password-strength strength-weak';
                }
            }
        });
    </script>
</body>
</html>
