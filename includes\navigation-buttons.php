<?php
/**
 * Navigation Buttons Component
 * Hiển thị nút "Quay về" và "Trang chủ"
 * 
 * @param string $back_url URL để quay về (optional)
 * @param bool $show_home Hiển thị nút trang chủ (default: true)
 * @param bool $show_back Hiển thị nút quay về (default: true)
 */

function renderNavigationButtons($back_url = null, $show_home = true, $show_back = true) {
    // Tự động xác định URL quay về nếu không được cung cấp
    if ($back_url === null) {
        $back_url = getAutoBackUrl();
    }
    
    echo '<div class="navigation-buttons">';
    
    // Nút Quay về
    if ($show_back && $back_url) {
        echo '<a href="' . htmlspecialchars($back_url) . '" class="nav-btn back-btn" title="Quay về trang trước">';
        echo '<span class="icon">←</span>';
        echo '<span class="text">Quay về</span>';
        echo '</a>';
    }
    
    // Nút Trang chủ
    if ($show_home) {
        $home_url = getHomeUrl();
        echo '<a href="' . htmlspecialchars($home_url) . '" class="nav-btn home-btn" title="Về trang chủ">';
        echo '<span class="icon">🏠</span>';
        echo '<span class="text">Trang chủ</span>';
        echo '</a>';
    }
    
    echo '</div>';
}

/**
 * Tự động xác định URL quay về dựa trên đường dẫn hiện tại
 */
function getAutoBackUrl() {
    $current_path = $_SERVER['REQUEST_URI'];
    $path_parts = explode('/', trim($current_path, '/'));
    
    // Loại bỏ query string
    $current_file = end($path_parts);
    if (strpos($current_file, '?') !== false) {
        $current_file = substr($current_file, 0, strpos($current_file, '?'));
    }
    
    // Xác định URL quay về dựa trên vị trí hiện tại
    if (strpos($current_path, '/modules/patients/') !== false) {
        switch ($current_file) {
            case 'add.php':
            case 'edit.php':
            case 'view.php':
            case 'delete.php':
                return 'list.php';
            default:
                return '../../dashboard.php';
        }
    }
    
    if (strpos($current_path, '/modules/appointments/') !== false) {
        switch ($current_file) {
            case 'add.php':
            case 'edit.php':
            case 'view.php':
            case 'delete.php':
                return 'list.php';
            default:
                return '../../dashboard.php';
        }
    }
    
    if (strpos($current_path, '/modules/payments/') !== false) {
        switch ($current_file) {
            case 'add.php':
            case 'process.php':
            case 'receipt.php':
                return 'list.php';
            default:
                return '../../dashboard.php';
        }
    }
    
    if (strpos($current_path, '/modules/reports/') !== false) {
        return '../../dashboard.php';
    }
    
    if (strpos($current_path, '/modules/users/') !== false) {
        return '../../dashboard.php';
    }
    
    // Default: quay về dashboard
    return '../../dashboard.php';
}

/**
 * Xác định URL trang chủ dựa trên vị trí hiện tại
 */
function getHomeUrl() {
    $current_path = $_SERVER['REQUEST_URI'];
    
    // Nếu đang ở trong modules
    if (strpos($current_path, '/modules/') !== false) {
        return '../../dashboard.php';
    }
    
    // Nếu đang ở root level
    return 'dashboard.php';
}

/**
 * Render navigation buttons với JavaScript back functionality
 */
function renderNavigationButtonsWithJS($custom_back_url = null, $show_home = true, $show_back = true) {
    echo '<div class="navigation-buttons">';
    
    // Nút Quay về với JavaScript
    if ($show_back) {
        if ($custom_back_url) {
            echo '<a href="' . htmlspecialchars($custom_back_url) . '" class="nav-btn back-btn" title="Quay về trang trước">';
        } else {
            echo '<a href="javascript:history.back()" class="nav-btn back-btn" title="Quay về trang trước">';
        }
        echo '<span class="icon">←</span>';
        echo '<span class="text">Quay về</span>';
        echo '</a>';
    }
    
    // Nút Trang chủ
    if ($show_home) {
        $home_url = getHomeUrl();
        echo '<a href="' . htmlspecialchars($home_url) . '" class="nav-btn home-btn" title="Về trang chủ">';
        echo '<span class="icon">🏠</span>';
        echo '<span class="text">Trang chủ</span>';
        echo '</a>';
    }
    
    echo '</div>';
}

/**
 * Render chỉ nút quay về
 */
function renderBackButton($back_url = null) {
    renderNavigationButtons($back_url, false, true);
}

/**
 * Render chỉ nút trang chủ
 */
function renderHomeButton() {
    renderNavigationButtons(null, true, false);
}

/**
 * CSS và JavaScript cho navigation buttons
 */
function includeNavigationAssets() {
    echo '<link rel="stylesheet" href="../../assets/css/navigation-buttons.css">';
    echo '<script>';
    echo 'document.addEventListener("DOMContentLoaded", function() {';
    echo '    // Add keyboard shortcuts';
    echo '    document.addEventListener("keydown", function(e) {';
    echo '        if (e.altKey && e.key === "ArrowLeft") {';
    echo '            e.preventDefault();';
    echo '            history.back();';
    echo '        }';
    echo '        if (e.altKey && e.key === "h") {';
    echo '            e.preventDefault();';
    echo '            window.location.href = "../../dashboard.php";';
    echo '        }';
    echo '    });';
    echo '});';
    echo '</script>';
}
?>
