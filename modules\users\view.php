<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';
require_once '../../includes/navigation-buttons.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

// Kiểm tra quyền admin
if ($current_user['role'] !== 'dentist') {
    header("Location: ../../dashboard.php?error=access_denied");
    exit();
}

$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$user_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

// Lấy thông tin user
$sql = "SELECT * FROM users WHERE id = ?";
$user = $database->fetchOne($sql, [$user_id]);

if (!$user) {
    header("Location: list.php?error=user_not_found");
    exit();
}

// Lấy thống kê hoạt động của user
$stats = [];

// Số lượng bệnh nhân được tạo bởi user này (nếu có tracking)
$stats['patients_created'] = 0; // Placeholder - cần thêm tracking

// Số lượng lịch hẹn được tạo bởi user này (nếu có tracking)
$stats['appointments_created'] = 0; // Placeholder - cần thêm tracking

// Thời gian đăng nhập cuối
$stats['last_login'] = $user['updated_at']; // Placeholder

function getRoleInfo($role) {
    $roles = [
        'dentist' => [
            'name' => 'Bác sĩ Nha khoa',
            'icon' => '👨‍⚕️',
            'color' => '#4CAF50',
            'description' => 'Có quyền quản lý toàn bộ hệ thống, thêm/sửa/xóa bệnh nhân, lịch hẹn, và quản lý nhân viên.'
        ],
        'assistant' => [
            'name' => 'Trợ lý Nha khoa',
            'icon' => '👩‍💼',
            'color' => '#2196F3',
            'description' => 'Hỗ trợ quản lý lịch hẹn, bệnh nhân và các công việc hành chính.'
        ]
    ];
    return $roles[$role] ?? [
        'name' => $role,
        'icon' => '👤',
        'color' => '#666',
        'description' => 'Vai trò không xác định'
    ];
}

function getStatusInfo($status) {
    $statuses = [
        'active' => [
            'name' => 'Hoạt động',
            'icon' => '✅',
            'color' => '#4CAF50',
            'description' => 'Tài khoản đang hoạt động bình thường'
        ],
        'inactive' => [
            'name' => 'Tạm khóa',
            'icon' => '❌',
            'color' => '#f44336',
            'description' => 'Tài khoản đã bị tạm khóa'
        ]
    ];
    return $statuses[$status] ?? [
        'name' => $status,
        'icon' => '❓',
        'color' => '#666',
        'description' => 'Trạng thái không xác định'
    ];
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👁️ Chi tiết Người dùng</title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/navigation-buttons.css">
    <style>
        .view-user-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .user-profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }
        
        .user-avatar-large {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
            font-weight: bold;
            margin: 0 auto 20px;
            border: 4px solid rgba(255, 255, 255, 0.3);
        }
        
        .user-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .user-username {
            font-size: 16px;
            opacity: 0.8;
            margin-bottom: 15px;
        }
        
        .user-role-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #4CAF50;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
            flex: 1;
        }
        
        .info-value {
            font-weight: 500;
            color: #333;
            flex: 2;
            text-align: right;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card.blue {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }
        
        .stat-card.orange {
            background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <?php renderNavigationButtons('list.php'); ?>

    <div class="view-user-container fade-in">
        <!-- Profile Header -->
        <div class="user-profile-header">
            <div class="user-avatar-large">
                <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
            </div>
            <div class="user-name"><?php echo htmlspecialchars($user['full_name']); ?></div>
            <div class="user-username">@<?php echo htmlspecialchars($user['username']); ?></div>
            
            <?php 
            $role_info = getRoleInfo($user['role']);
            $status_info = getStatusInfo($user['status']);
            ?>
            
            <div class="user-role-badge">
                <span><?php echo $role_info['icon']; ?></span>
                <span><?php echo $role_info['name']; ?></span>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['patients_created']; ?></div>
                <div class="stat-label">👥 Bệnh nhân tạo</div>
            </div>
            <div class="stat-card blue">
                <div class="stat-number"><?php echo $stats['appointments_created']; ?></div>
                <div class="stat-label">📅 Lịch hẹn tạo</div>
            </div>
            <div class="stat-card orange">
                <div class="stat-number"><?php echo date('d/m/Y', strtotime($user['created_at'])); ?></div>
                <div class="stat-label">📅 Ngày tham gia</div>
            </div>
        </div>

        <!-- Information Grid -->
        <div class="info-grid">
            <!-- Personal Information -->
            <div class="info-section">
                <div class="section-title">
                    <span>👤</span>
                    Thông tin Cá nhân
                </div>
                
                <div class="info-item">
                    <span class="info-label">Họ và tên:</span>
                    <span class="info-value"><?php echo htmlspecialchars($user['full_name']); ?></span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">Email:</span>
                    <span class="info-value">
                        <?php echo !empty($user['email']) ? htmlspecialchars($user['email']) : '<em>Chưa cập nhật</em>'; ?>
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">Số điện thoại:</span>
                    <span class="info-value">
                        <?php echo !empty($user['phone']) ? htmlspecialchars($user['phone']) : '<em>Chưa cập nhật</em>'; ?>
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">Địa chỉ:</span>
                    <span class="info-value">
                        <?php echo !empty($user['address']) ? htmlspecialchars($user['address']) : '<em>Chưa cập nhật</em>'; ?>
                    </span>
                </div>
            </div>

            <!-- System Information -->
            <div class="info-section">
                <div class="section-title">
                    <span>⚙️</span>
                    Thông tin Hệ thống
                </div>
                
                <div class="info-item">
                    <span class="info-label">Username:</span>
                    <span class="info-value">@<?php echo htmlspecialchars($user['username']); ?></span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">Vai trò:</span>
                    <span class="info-value">
                        <span style="color: <?php echo $role_info['color']; ?>;">
                            <?php echo $role_info['icon'] . ' ' . $role_info['name']; ?>
                        </span>
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">Trạng thái:</span>
                    <span class="info-value">
                        <span class="status-badge" style="background: <?php echo $status_info['color']; ?>; color: white;">
                            <?php echo $status_info['icon'] . ' ' . $status_info['name']; ?>
                        </span>
                    </span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">Ngày tạo:</span>
                    <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">Cập nhật cuối:</span>
                    <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($user['updated_at'])); ?></span>
                </div>
            </div>
        </div>

        <!-- Professional Information (for dentists) -->
        <?php if ($user['role'] == 'dentist'): ?>
            <div class="info-section">
                <div class="section-title">
                    <span>🏥</span>
                    Thông tin Chuyên môn
                </div>
                
                <div class="info-grid">
                    <div>
                        <div class="info-item">
                            <span class="info-label">Tên phòng khám:</span>
                            <span class="info-value">
                                <?php echo !empty($user['clinic_name']) ? htmlspecialchars($user['clinic_name']) : '<em>Chưa cập nhật</em>'; ?>
                            </span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">SĐT phòng khám:</span>
                            <span class="info-value">
                                <?php echo !empty($user['clinic_phone']) ? htmlspecialchars($user['clinic_phone']) : '<em>Chưa cập nhật</em>'; ?>
                            </span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">Chuyên khoa:</span>
                            <span class="info-value">
                                <?php echo !empty($user['specialty']) ? htmlspecialchars($user['specialty']) : '<em>Chưa cập nhật</em>'; ?>
                            </span>
                        </div>
                    </div>
                    
                    <div>
                        <div class="info-item">
                            <span class="info-label">Số chứng chỉ:</span>
                            <span class="info-value">
                                <?php echo !empty($user['license_number']) ? htmlspecialchars($user['license_number']) : '<em>Chưa cập nhật</em>'; ?>
                            </span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">Kinh nghiệm:</span>
                            <span class="info-value"><?php echo $user['experience_years']; ?> năm</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">Trình độ:</span>
                            <span class="info-value">
                                <?php echo !empty($user['education']) ? htmlspecialchars($user['education']) : '<em>Chưa cập nhật</em>'; ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($user['clinic_address'])): ?>
                    <div class="info-item">
                        <span class="info-label">Địa chỉ phòng khám:</span>
                        <span class="info-value"><?php echo htmlspecialchars($user['clinic_address']); ?></span>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($user['bio'])): ?>
                    <div class="info-item">
                        <span class="info-label">Giới thiệu:</span>
                        <span class="info-value"><?php echo nl2br(htmlspecialchars($user['bio'])); ?></span>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="edit.php?id=<?php echo $user['id']; ?>" class="btn btn-primary" style="padding: 12px 24px;">
                ✏️ Chỉnh sửa
            </a>
            
            <?php if ($user['id'] != $current_user['id']): ?>
                <a href="toggle-status.php?id=<?php echo $user['id']; ?>" 
                   class="btn btn-warning" style="padding: 12px 24px;"
                   onclick="return confirm('Bạn có chắc muốn thay đổi trạng thái tài khoản này?')">
                    <?php echo ($user['status'] == 'active') ? '🔒 Khóa tài khoản' : '🔓 Kích hoạt tài khoản'; ?>
                </a>
            <?php endif; ?>
            
            <a href="list.php" class="btn btn-secondary" style="padding: 12px 24px;">
                ← Quay lại danh sách
            </a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.view-user-container').classList.add('fade-in');
        });
    </script>
</body>
</html>
