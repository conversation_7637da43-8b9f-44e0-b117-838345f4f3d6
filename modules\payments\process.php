<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';
require_once '../../includes/navigation-buttons.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$database = new Database();

$appointment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$appointment_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

// Lấy thông tin lịch hẹn
$sql = "SELECT a.*, b.hoten as patient_name, b.sdt as patient_phone, b.email as patient_email
        FROM appointments a 
        LEFT JOIN benhnhan b ON a.patient_id = b.id 
        WHERE a.id = ?";

$appointment = $database->fetchOne($sql, [$appointment_id]);

if (!$appointment) {
    header("Location: list.php?error=not_found");
    exit();
}

// X<PERSON> lý form thanh toán
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $actual_cost = (float)($_POST['actual_cost'] ?? 0);
    $payment_method = $_POST['payment_method'] ?? '';
    $payment_status = $_POST['payment_status'] ?? '';
    $payment_notes = trim($_POST['payment_notes'] ?? '');
    
    // Validation
    if ($actual_cost <= 0) {
        $error = "Số tiền thanh toán phải lớn hơn 0!";
    } elseif (empty($payment_method)) {
        $error = "Vui lòng chọn phương thức thanh toán!";
    } elseif (empty($payment_status)) {
        $error = "Vui lòng chọn trạng thái thanh toán!";
    } else {
        try {
            $database->beginTransaction();

            // Kiểm tra xem các cột có tồn tại không
            $check_columns_sql = "SHOW COLUMNS FROM appointments LIKE 'payment_method'";
            $has_payment_columns = $database->fetchOne($check_columns_sql);

            if ($has_payment_columns) {
                // Cập nhật với đầy đủ cột thanh toán
                $update_sql = "UPDATE appointments SET
                              actual_cost = ?,
                              payment_status = ?,
                              payment_method = ?,
                              payment_notes = ?,
                              payment_date = NOW(),
                              updated_at = NOW()
                              WHERE id = ?";

                $result = $database->execute($update_sql, [
                    $actual_cost,
                    $payment_status,
                    $payment_method,
                    $payment_notes,
                    $appointment_id
                ]);
            } else {
                // Chỉ cập nhật các cột cơ bản có sẵn
                $update_sql = "UPDATE appointments SET
                              actual_cost = ?,
                              payment_status = ?,
                              updated_at = NOW()
                              WHERE id = ?";

                $result = $database->execute($update_sql, [
                    $actual_cost,
                    $payment_status,
                    $appointment_id
                ]);
            }

            if ($result) {
                $database->commit();
                $success = "Đã ghi nhận thanh toán thành công!";

                // Cập nhật thông tin appointment
                $appointment['actual_cost'] = $actual_cost;
                $appointment['payment_status'] = $payment_status;
                if ($has_payment_columns) {
                    $appointment['payment_method'] = $payment_method;
                    $appointment['payment_notes'] = $payment_notes;
                }
            } else {
                $database->rollback();
                $error = "Không thể cập nhật thông tin thanh toán!";
            }

        } catch(Exception $e) {
            $database->rollback();
            $error = "Lỗi hệ thống: " . $e->getMessage();
        }
    }
}

function getServiceTypeLabel($service_type) {
    $labels = [
        'kham_tong_quat' => '🔍 Khám tổng quát',
        'tay_trang' => '✨ Tẩy trắng',
        'nho_rang' => '🦷 Nhổ răng',
        'han_tram' => '🔧 Hàn trám',
        'dieu_tri_tuy' => '🩺 Điều trị tủy',
        'nieng_rang' => '📐 Niềng răng',
        'cay_ghep' => '🔩 Cấy ghép',
        'phau_thuat' => '⚕️ Phẫu thuật',
        'tu_van' => '💬 Tư vấn'
    ];
    return $labels[$service_type] ?? '🔍 Khám tổng quát';
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💳 Xử lý Thanh toán - LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <link rel="stylesheet" href="../../assets/css/navigation-buttons.css">
    <style>
        .payment-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .appointment-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #4CAF50;
        }
        
        .info-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
        }
        
        .info-value {
            font-weight: 500;
            color: #333;
        }
        
        .payment-form {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .amount-display {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .amount-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .amount-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .payment-method {
            display: none;
        }
        
        .payment-method-label {
            display: block;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .payment-method:checked + .payment-method-label {
            border-color: #4CAF50;
            background: #4CAF50;
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .info-row, .form-row {
                grid-template-columns: 1fr;
            }
            
            .payment-methods {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <?php renderNavigationButtons('list.php'); ?>

    <div class="payment-container fade-in">
        <!-- Thông báo -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <div class="module-header">
            <h1>
                <span style="font-size: 40px;">💳</span>
                Xử lý Thanh toán
            </h1>
            <p style="color: #666;">
                Mã lịch hẹn: <strong>LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></strong>
            </p>
        </div>

        <!-- Thông tin lịch hẹn -->
        <div class="appointment-info">
            <h3 style="margin-top: 0; color: #4CAF50;">📋 Thông tin lịch hẹn</h3>
            
            <div class="info-row">
                <div>
                    <div class="info-item">
                        <span class="info-label">Bệnh nhân:</span>
                        <span class="info-value"><?php echo htmlspecialchars($appointment['patient_name']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Số điện thoại:</span>
                        <span class="info-value"><?php echo htmlspecialchars($appointment['patient_phone']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Dịch vụ:</span>
                        <span class="info-value"><?php echo getServiceTypeLabel($appointment['service_type']); ?></span>
                    </div>
                </div>
                
                <div>
                    <div class="info-item">
                        <span class="info-label">Ngày khám:</span>
                        <span class="info-value"><?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Giờ khám:</span>
                        <span class="info-value"><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Chi phí dự kiến:</span>
                        <span class="info-value">
                            <?php echo $appointment['estimated_cost'] ? number_format($appointment['estimated_cost'], 0, ',', '.') . 'đ' : 'Chưa xác định'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form thanh toán -->
        <form method="POST" class="payment-form">
            <h3 style="margin-top: 0; color: #333;">💰 Thông tin thanh toán</h3>
            
            <!-- Hiển thị số tiền -->
            <div class="amount-display">
                <div class="amount-number" id="amount-display">
                    <?php echo $appointment['actual_cost'] ? number_format($appointment['actual_cost'], 0, ',', '.') : ($appointment['estimated_cost'] ? number_format($appointment['estimated_cost'], 0, ',', '.') : '0'); ?>đ
                </div>
                <div class="amount-label">Số tiền thanh toán</div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="actual_cost">Số tiền thực tế *</label>
                    <input type="number" id="actual_cost" name="actual_cost" required 
                           min="0" step="1000" 
                           value="<?php echo $appointment['actual_cost'] ?: $appointment['estimated_cost']; ?>"
                           placeholder="Nhập số tiền thực tế...">
                </div>
                
                <div class="form-group">
                    <label for="payment_status">Trạng thái thanh toán *</label>
                    <select id="payment_status" name="payment_status" required>
                        <option value="">-- Chọn trạng thái --</option>
                        <option value="da_thanh_toan" <?php echo ($appointment['payment_status'] == 'da_thanh_toan') ? 'selected' : ''; ?>>✅ Đã thanh toán đầy đủ</option>
                        <option value="thanh_toan_mot_phan" <?php echo ($appointment['payment_status'] == 'thanh_toan_mot_phan') ? 'selected' : ''; ?>>📊 Thanh toán một phần</option>
                        <option value="chua_thanh_toan" <?php echo ($appointment['payment_status'] == 'chua_thanh_toan') ? 'selected' : ''; ?>>💳 Chưa thanh toán</option>
                    </select>
                </div>
            </div>
            
            <!-- Phương thức thanh toán -->
            <div class="form-group">
                <label>Phương thức thanh toán *</label>
                <div class="payment-methods">
                    <input type="radio" id="cash" name="payment_method" value="tien_mat" class="payment-method"
                           <?php echo (isset($appointment['payment_method']) && $appointment['payment_method'] == 'tien_mat') ? 'checked' : ''; ?>>
                    <label for="cash" class="payment-method-label">
                        <div style="font-size: 20px; margin-bottom: 5px;">💵</div>
                        <div>Tiền mặt</div>
                    </label>

                    <input type="radio" id="transfer" name="payment_method" value="chuyen_khoan" class="payment-method"
                           <?php echo (isset($appointment['payment_method']) && $appointment['payment_method'] == 'chuyen_khoan') ? 'checked' : ''; ?>>
                    <label for="transfer" class="payment-method-label">
                        <div style="font-size: 20px; margin-bottom: 5px;">🏦</div>
                        <div>Chuyển khoản</div>
                    </label>

                    <input type="radio" id="card" name="payment_method" value="the_ngan_hang" class="payment-method"
                           <?php echo (isset($appointment['payment_method']) && $appointment['payment_method'] == 'the_ngan_hang') ? 'checked' : ''; ?>>
                    <label for="card" class="payment-method-label">
                        <div style="font-size: 20px; margin-bottom: 5px;">💳</div>
                        <div>Thẻ ngân hàng</div>
                    </label>

                    <input type="radio" id="other" name="payment_method" value="khac" class="payment-method"
                           <?php echo (isset($appointment['payment_method']) && $appointment['payment_method'] == 'khac') ? 'checked' : ''; ?>>
                    <label for="other" class="payment-method-label">
                        <div style="font-size: 20px; margin-bottom: 5px;">📱</div>
                        <div>Khác</div>
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="payment_notes">Ghi chú thanh toán</label>
                <textarea id="payment_notes" name="payment_notes" rows="3" 
                          placeholder="Ghi chú về thanh toán (tùy chọn)..."><?php echo htmlspecialchars($appointment['payment_notes'] ?? ''); ?></textarea>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn btn-primary" style="padding: 15px 30px; font-size: 16px;">
                    💾 Lưu thông tin thanh toán
                </button>
                <a href="list.php" class="btn btn-secondary" style="padding: 15px 30px; font-size: 16px; margin-left: 10px;">
                    ← Quay lại danh sách
                </a>
            </div>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.payment-container').classList.add('fade-in');
            
            // Cập nhật hiển thị số tiền khi thay đổi
            const actualCostInput = document.getElementById('actual_cost');
            const amountDisplay = document.getElementById('amount-display');
            
            actualCostInput.addEventListener('input', function() {
                const amount = parseInt(this.value) || 0;
                amountDisplay.textContent = amount.toLocaleString('vi-VN') + 'đ';
            });
        });
    </script>
</body>
</html>
