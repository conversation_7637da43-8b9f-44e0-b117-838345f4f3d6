/**
 * Patients Module
 * Handles patient management functionality
 */

class PatientsModule {
    constructor() {
        this.currentPage = 1;
        this.searchTerm = '';
        this.patients = [];
        this.totalPages = 1;
    }

    /**
     * Render patients page
     */
    async render() {
        const content = `
            <div class="fade-in">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users me-2"></i>Quản lý Bệnh nhân</h2>
                    <button class="btn btn-primary" onclick="patients.showAddModal()">
                        <i class="fas fa-plus me-1"></i>Thêm bệnh nhân
                    </button>
                </div>
                
                <!-- Search and Filters -->
                <div class="search-box">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="input-group">
                                <input type="text" class="form-control" id="patientSearch" 
                                       placeholder="T<PERSON><PERSON> kiế<PERSON> theo tê<PERSON>, số điện thoại, email, CMND...">
                                <button class="btn btn-outline-secondary" onclick="patients.search()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-outline-primary" onclick="patients.clearSearch()">
                                <i class="fas fa-times me-1"></i>Xóa bộ lọc
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Patients Table -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-list me-2"></i>Danh sách Bệnh nhân
                    </div>
                    <div class="card-body">
                        <div id="patientsTableContainer">
                            <div class="text-center py-4">
                                <div class="spinner-border" role="status"></div>
                                <div class="mt-2">Đang tải dữ liệu...</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Patients pagination" class="mt-4">
                    <ul class="pagination" id="patientsPagination"></ul>
                </nav>
            </div>
        `;
        
        document.getElementById('pageContent').innerHTML = content;
        
        // Setup search
        this.setupSearch();
        
        // Load patients data
        await this.loadPatients();
    }

    /**
     * Setup search functionality
     */
    setupSearch() {
        const searchInput = document.getElementById('patientSearch');
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.search();
                }
            });
        }
    }

    /**
     * Load patients data
     */
    async loadPatients() {
        try {
            const params = {
                page: this.currentPage,
                limit: 10
            };
            
            if (this.searchTerm) {
                params.search = this.searchTerm;
            }
            
            const response = await api.getPatients(params);
            
            if (response.success) {
                this.patients = response.data;
                this.totalPages = response.pagination.total_pages;
                
                this.renderPatientsTable();
                this.renderPagination();
            }
        } catch (error) {
            api.handleError(error);
        }
    }

    /**
     * Render patients table
     */
    renderPatientsTable() {
        const container = document.getElementById('patientsTableContainer');
        
        if (this.patients.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5>Không có bệnh nhân nào</h5>
                    <p class="text-muted">Hãy thêm bệnh nhân đầu tiên</p>
                </div>
            `;
            return;
        }
        
        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Họ tên</th>
                            <th>Số điện thoại</th>
                            <th>Email</th>
                            <th>Ngày sinh</th>
                            <th>Giới tính</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${this.patients.map(patient => this.renderPatientRow(patient)).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        container.innerHTML = tableHtml;
    }

    /**
     * Render single patient row
     */
    renderPatientRow(patient) {
        return `
            <tr>
                <td>
                    <div class="fw-bold">${patient.hoten}</div>
                    <small class="text-muted">${patient.cmnd || 'Chưa có CMND'}</small>
                </td>
                <td>${patient.sdt || '-'}</td>
                <td>${patient.email || '-'}</td>
                <td>${patient.ngaysinh || '-'}</td>
                <td>${patient.gioitinh}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="patients.showViewModal(${patient.id})" title="Xem chi tiết">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="patients.showEditModal(${patient.id})" title="Sửa">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${auth.isDentist() ? `
                        <button class="btn btn-outline-danger" onclick="patients.confirmDelete(${patient.id})" title="Xóa">
                            <i class="fas fa-trash"></i>
                        </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Search patients
     */
    search() {
        const searchInput = document.getElementById('patientSearch');
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.loadPatients();
    }

    /**
     * Clear search
     */
    clearSearch() {
        document.getElementById('patientSearch').value = '';
        this.searchTerm = '';
        this.currentPage = 1;
        this.loadPatients();
    }

    /**
     * Show add patient modal
     */
    showAddModal() {
        this.showPatientModal();
    }

    /**
     * Show edit patient modal
     */
    showEditModal(patientId) {
        const patient = this.patients.find(p => p.id === patientId);
        if (patient) {
            this.showPatientModal(patient);
        }
    }

    /**
     * Show patient modal (add/edit)
     */
    showPatientModal(patient = null) {
        const isEdit = patient !== null;
        const title = isEdit ? 'Sửa thông tin bệnh nhân' : 'Thêm bệnh nhân mới';
        
        const modalHtml = `
            <div class="modal fade" id="patientModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form id="patientForm">
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Họ tên *</label>
                                            <input type="text" class="form-control" name="hoten" value="${patient?.hoten || ''}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Số điện thoại *</label>
                                            <input type="tel" class="form-control" name="sdt" value="${patient?.sdt || ''}" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" name="email" value="${patient?.email || ''}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Ngày sinh</label>
                                            <input type="date" class="form-control" name="ngaysinh" value="${patient?.ngaysinh || ''}">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Giới tính</label>
                                            <select class="form-select" name="gioitinh">
                                                <option value="Nam" ${patient?.gioitinh === 'Nam' ? 'selected' : ''}>Nam</option>
                                                <option value="Nữ" ${patient?.gioitinh === 'Nữ' ? 'selected' : ''}>Nữ</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">CMND/CCCD</label>
                                            <input type="text" class="form-control" name="cmnd" value="${patient?.cmnd || ''}">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Địa chỉ</label>
                                    <textarea class="form-control" name="diachi" rows="2">${patient?.diachi || ''}</textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Nghề nghiệp</label>
                                    <input type="text" class="form-control" name="nghe_nghiep" value="${patient?.nghe_nghiep || ''}">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Tiền sử bệnh</label>
                                    <textarea class="form-control" name="tien_su_benh" rows="2">${patient?.tien_su_benh || ''}</textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Dị ứng</label>
                                    <textarea class="form-control" name="di_ung" rows="2">${patient?.di_ung || ''}</textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Liên hệ khẩn cấp</label>
                                    <input type="text" class="form-control" name="lien_he_khan_cap" value="${patient?.lien_he_khan_cap || ''}">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Ghi chú</label>
                                    <textarea class="form-control" name="ghi_chu" rows="2">${patient?.ghi_chu || ''}</textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>${isEdit ? 'Cập nhật' : 'Thêm mới'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to page
        document.getElementById('modalContainer').innerHTML = modalHtml;
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('patientModal'));
        modal.show();
        
        // Setup form submission
        document.getElementById('patientForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.savePatient(patient?.id, modal);
        });
    }

    /**
     * Save patient (create or update)
     */
    async savePatient(patientId, modal) {
        try {
            const form = document.getElementById('patientForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            let response;
            if (patientId) {
                response = await api.updatePatient(patientId, data);
            } else {
                response = await api.createPatient(data);
            }
            
            if (response.success) {
                modal.hide();
                api.showSuccess(response.message);
                await this.loadPatients();
            }
        } catch (error) {
            api.handleError(error);
        }
    }

    /**
     * Confirm delete patient
     */
    confirmDelete(patientId) {
        const patient = this.patients.find(p => p.id === patientId);
        if (patient && confirm(`Bạn có chắc chắn muốn xóa bệnh nhân "${patient.hoten}"?`)) {
            this.deletePatient(patientId);
        }
    }

    /**
     * Delete patient
     */
    async deletePatient(patientId) {
        try {
            const response = await api.deletePatient(patientId);
            if (response.success) {
                api.showSuccess(response.message);
                await this.loadPatients();
            }
        } catch (error) {
            api.handleError(error);
        }
    }

    /**
     * Render pagination
     */
    renderPagination() {
        const container = document.getElementById('patientsPagination');
        if (this.totalPages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // Previous button
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="patients.goToPage(${this.currentPage - 1})">Trước</a>
            </li>
        `;
        
        // Page numbers
        for (let i = 1; i <= this.totalPages; i++) {
            if (i === this.currentPage || i === 1 || i === this.totalPages || 
                (i >= this.currentPage - 1 && i <= this.currentPage + 1)) {
                html += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="patients.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 2 || i === this.currentPage + 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // Next button
        html += `
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="patients.goToPage(${this.currentPage + 1})">Sau</a>
            </li>
        `;
        
        container.innerHTML = html;
    }

    /**
     * Go to specific page
     */
    goToPage(page) {
        if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
            this.currentPage = page;
            this.loadPatients();
        }
    }
}

// Create global patients module instance
window.patients = new PatientsModule();
