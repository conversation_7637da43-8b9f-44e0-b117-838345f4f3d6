/**
 * Reports Module
 * Handles reports and statistics functionality
 */

class ReportsModule {
    constructor() {
        this.currentStats = null;
        this.dateFrom = '';
        this.dateTo = '';
    }

    /**
     * Render reports page
     */
    async render() {
        const content = `
            <div class="fade-in">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chart-bar me-2"></i>Báo cáo & Thống kê</h2>
                    <button class="btn btn-primary" onclick="reports.exportReport()">
                        <i class="fas fa-download me-1"></i>Xuất báo cáo
                    </button>
                </div>
                
                <!-- Date Range Filter -->
                <div class="search-box">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Từ ngày</label>
                            <input type="date" class="form-control" id="dateFrom" onchange="reports.updateDateRange()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label"><PERSON><PERSON><PERSON> ng<PERSON>y</label>
                            <input type="date" class="form-control" id="dateTo" onchange="reports.updateDateRange()">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button class="btn btn-outline-primary" onclick="reports.loadStats()">
                                    <i class="fas fa-sync-alt me-1"></i>Cập nhật
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button class="btn btn-outline-secondary" onclick="reports.setCurrentMonth()">
                                    <i class="fas fa-calendar me-1"></i>Tháng này
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Overview Stats -->
                <div class="row mb-4" id="overviewStats">
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number" id="totalPatientsReport">-</div>
                                    <div class="stats-label">Tổng bệnh nhân</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number" id="totalAppointments">-</div>
                                    <div class="stats-label">Tổng lịch hẹn</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number" id="completedAppointments">-</div>
                                    <div class="stats-label">Đã hoàn thành</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="stats-number" id="totalRevenue">-</div>
                                    <div class="stats-label">Tổng doanh thu</div>
                                </div>
                                <div class="stats-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts and Details -->
                <div class="row">
                    <!-- Appointment Status Chart -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-pie-chart me-2"></i>Trạng thái Lịch hẹn
                            </div>
                            <div class="card-body">
                                <div id="appointmentStatusChart">
                                    <div class="text-center py-4">
                                        <div class="spinner-border" role="status"></div>
                                        <div class="mt-2">Đang tải...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Service Types Chart -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-chart-bar me-2"></i>Dịch vụ phổ biến
                            </div>
                            <div class="card-body">
                                <div id="serviceTypesChart">
                                    <div class="text-center py-4">
                                        <div class="spinner-border" role="status"></div>
                                        <div class="mt-2">Đang tải...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Monthly Revenue Trend -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-line-chart me-2"></i>Xu hướng Doanh thu (6 tháng gần nhất)
                            </div>
                            <div class="card-body">
                                <div id="monthlyRevenueChart">
                                    <div class="text-center py-4">
                                        <div class="spinner-border" role="status"></div>
                                        <div class="mt-2">Đang tải...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-user-plus me-2"></i>Bệnh nhân mới gần đây
                            </div>
                            <div class="card-body">
                                <div id="recentPatients">
                                    <div class="text-center py-4">
                                        <div class="spinner-border" role="status"></div>
                                        <div class="mt-2">Đang tải...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-clock me-2"></i>Lịch hẹn sắp tới
                            </div>
                            <div class="card-body">
                                <div id="upcomingAppointmentsReport">
                                    <div class="text-center py-4">
                                        <div class="spinner-border" role="status"></div>
                                        <div class="mt-2">Đang tải...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('pageContent').innerHTML = content;
        
        // Set default date range (current month)
        this.setCurrentMonth();
        
        // Load initial stats
        await this.loadStats();
    }

    /**
     * Set current month as date range
     */
    setCurrentMonth() {
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        
        this.dateFrom = firstDay.toISOString().split('T')[0];
        this.dateTo = lastDay.toISOString().split('T')[0];
        
        document.getElementById('dateFrom').value = this.dateFrom;
        document.getElementById('dateTo').value = this.dateTo;
    }

    /**
     * Update date range from inputs
     */
    updateDateRange() {
        this.dateFrom = document.getElementById('dateFrom').value;
        this.dateTo = document.getElementById('dateTo').value;
    }

    /**
     * Load statistics data
     */
    async loadStats() {
        try {
            const params = {};
            if (this.dateFrom) params.date_from = this.dateFrom;
            if (this.dateTo) params.date_to = this.dateTo;
            
            const response = await api.getStats(params);
            
            if (response.success) {
                this.currentStats = response.data;
                this.renderStats();
            }
        } catch (error) {
            api.handleError(error);
        }
    }

    /**
     * Render all statistics
     */
    renderStats() {
        if (!this.currentStats) return;
        
        // Update overview stats
        this.renderOverviewStats();
        
        // Render charts
        this.renderAppointmentStatusChart();
        this.renderServiceTypesChart();
        this.renderMonthlyRevenueChart();
        
        // Render recent activity
        this.renderRecentPatients();
        this.renderUpcomingAppointments();
    }

    /**
     * Render overview statistics
     */
    renderOverviewStats() {
        const stats = this.currentStats;
        
        document.getElementById('totalPatientsReport').textContent = stats.total_patients || 0;
        
        // Calculate total appointments from status breakdown
        const totalAppts = Object.values(stats.appointments_by_status || {}).reduce((sum, count) => sum + count, 0);
        document.getElementById('totalAppointments').textContent = totalAppts;
        
        document.getElementById('completedAppointments').textContent = stats.appointments_by_status?.hoan_thanh || 0;
        document.getElementById('totalRevenue').textContent = api.formatCurrency(stats.revenue || 0);
    }

    /**
     * Render appointment status chart
     */
    renderAppointmentStatusChart() {
        const container = document.getElementById('appointmentStatusChart');
        const statusData = this.currentStats.appointments_by_status || {};
        
        if (Object.keys(statusData).length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Không có dữ liệu</p>';
            return;
        }
        
        const statusLabels = {
            'dat_lich': 'Đặt lịch',
            'xac_nhan': 'Xác nhận',
            'dang_kham': 'Đang khám',
            'hoan_thanh': 'Hoàn thành',
            'huy_lich': 'Hủy lịch',
            'khong_den': 'Không đến'
        };
        
        const html = Object.entries(statusData).map(([status, count]) => {
            const percentage = (count / Object.values(statusData).reduce((sum, c) => sum + c, 0) * 100).toFixed(1);
            return `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge ${this.getStatusClass(status)} me-2">${statusLabels[status] || status}</span>
                    <div class="flex-grow-1 mx-2">
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                    <span class="fw-bold">${count} (${percentage}%)</span>
                </div>
            `;
        }).join('');
        
        container.innerHTML = html;
    }

    /**
     * Render service types chart
     */
    renderServiceTypesChart() {
        const container = document.getElementById('serviceTypesChart');
        const services = this.currentStats.services || [];
        
        if (services.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Không có dữ liệu</p>';
            return;
        }
        
        const total = services.reduce((sum, service) => sum + parseInt(service.count), 0);
        
        const html = services.slice(0, 5).map(service => {
            const percentage = (service.count / total * 100).toFixed(1);
            return `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>${app.formatServiceType(service.service_type)}</span>
                    <div class="flex-grow-1 mx-2">
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-info" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                    <span class="fw-bold">${service.count} (${percentage}%)</span>
                </div>
            `;
        }).join('');
        
        container.innerHTML = html;
    }

    /**
     * Render monthly revenue chart
     */
    renderMonthlyRevenueChart() {
        const container = document.getElementById('monthlyRevenueChart');
        const monthlyData = this.currentStats.monthly_trend || [];
        
        if (monthlyData.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Không có dữ liệu</p>';
            return;
        }
        
        const html = `
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Tháng</th>
                            <th>Số lịch hẹn</th>
                            <th>Doanh thu</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${monthlyData.map(month => `
                            <tr>
                                <td>${month.month}</td>
                                <td>${month.appointments}</td>
                                <td class="fw-bold text-success">${api.formatCurrency(month.revenue || 0)}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        container.innerHTML = html;
    }

    /**
     * Render recent patients
     */
    renderRecentPatients() {
        const container = document.getElementById('recentPatients');
        const patients = this.currentStats.recent_patients || [];
        
        if (patients.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Không có dữ liệu</p>';
            return;
        }
        
        const html = patients.map(patient => `
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                <div>
                    <div class="fw-bold">${patient.hoten}</div>
                    <small class="text-muted">${patient.sdt}</small>
                </div>
                <small class="text-muted">${patient.created_at}</small>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }

    /**
     * Render upcoming appointments
     */
    renderUpcomingAppointments() {
        const container = document.getElementById('upcomingAppointmentsReport');
        const appointments = this.currentStats.upcoming_appointments || [];
        
        if (appointments.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">Không có lịch hẹn sắp tới</p>';
            return;
        }
        
        const html = appointments.map(appointment => `
            <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                <div>
                    <div class="fw-bold">${appointment.patient_name}</div>
                    <small class="text-muted">${appointment.appointment_date} ${appointment.appointment_time}</small>
                </div>
                <span class="badge bg-primary">${app.formatServiceType(appointment.service_type)}</span>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }

    /**
     * Get status CSS class
     */
    getStatusClass(status) {
        const statusClasses = {
            'dat_lich': 'status-dat-lich',
            'xac_nhan': 'status-xac-nhan',
            'dang_kham': 'status-dang-kham',
            'hoan_thanh': 'status-hoan-thanh',
            'huy_lich': 'status-huy-lich',
            'khong_den': 'status-khong-den'
        };
        return statusClasses[status] || 'bg-secondary';
    }

    /**
     * Export report (placeholder)
     */
    exportReport() {
        api.showError('Chức năng xuất báo cáo chưa được triển khai');
    }
}

// Create global reports module instance
window.reports = new ReportsModule();
