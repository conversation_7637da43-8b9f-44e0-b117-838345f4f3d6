/**
 * API Client for Phòng khám Management System
 * Handles all API communications with backend
 */

class ApiClient {
    constructor() {
        this.baseUrl = '../api';
        this.token = localStorage.getItem('api_token');
    }

    /**
     * Set authentication token
     */
    setToken(token) {
        this.token = token;
        if (token) {
            localStorage.setItem('api_token', token);
        } else {
            localStorage.removeItem('api_token');
        }
    }

    /**
     * Get authentication headers
     */
    getHeaders(includeAuth = true) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };

        if (includeAuth && this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        return headers;
    }

    /**
     * Make HTTP request
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        
        const config = {
            method: options.method || 'GET',
            headers: this.getHeaders(options.auth !== false),
            ...options
        };

        if (config.method !== 'GET' && options.data) {
            config.body = JSON.stringify(options.data);
        }

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    /**
     * Authentication APIs
     */
    async login(username, password) {
        return this.request('/auth/login.php', {
            method: 'POST',
            data: { username, password },
            auth: false
        });
    }

    async logout() {
        return this.request('/auth/logout.php', {
            method: 'POST'
        });
    }

    async verifyToken() {
        return this.request('/auth/verify.php');
    }

    /**
     * Patients APIs
     */
    async getPatients(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `/patients/index.php?${queryString}` : '/patients/index.php';
        return this.request(endpoint);
    }

    async createPatient(patientData) {
        return this.request('/patients/index.php', {
            method: 'POST',
            data: patientData
        });
    }

    async updatePatient(id, patientData) {
        return this.request(`/patients/update.php?id=${id}`, {
            method: 'PUT',
            data: patientData
        });
    }

    async deletePatient(id) {
        return this.request(`/patients/delete.php?id=${id}`, {
            method: 'DELETE'
        });
    }

    /**
     * Appointments APIs
     */
    async getAppointments(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `/appointments/index.php?${queryString}` : '/appointments/index.php';
        return this.request(endpoint);
    }

    async createAppointment(appointmentData) {
        return this.request('/appointments/index.php', {
            method: 'POST',
            data: appointmentData
        });
    }

    /**
     * Reports APIs
     */
    async getStats(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const endpoint = queryString ? `/reports/stats.php?${queryString}` : '/reports/stats.php';
        return this.request(endpoint);
    }

    /**
     * Utility methods
     */
    formatDate(date) {
        if (!date) return '';
        return new Date(date).toLocaleDateString('vi-VN');
    }

    formatCurrency(amount) {
        if (!amount) return '0';
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    }

    formatDateTime(datetime) {
        if (!datetime) return '';
        return new Date(datetime).toLocaleString('vi-VN');
    }

    /**
     * Error handling
     */
    handleError(error) {
        console.error('API Error:', error);
        
        if (error.message.includes('Token')) {
            // Token expired or invalid
            this.setToken(null);
            window.location.reload();
            return;
        }

        // Show error message to user
        this.showError(error.message || 'Có lỗi xảy ra, vui lòng thử lại');
    }

    showError(message) {
        // Create or update error alert
        let errorAlert = document.getElementById('globalError');
        if (!errorAlert) {
            errorAlert = document.createElement('div');
            errorAlert.id = 'globalError';
            errorAlert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            errorAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            document.body.appendChild(errorAlert);
        }

        errorAlert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Auto hide after 5 seconds
        setTimeout(() => {
            if (errorAlert && errorAlert.parentNode) {
                errorAlert.remove();
            }
        }, 5000);
    }

    showSuccess(message) {
        // Create success alert
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successAlert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        successAlert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(successAlert);

        // Auto hide after 3 seconds
        setTimeout(() => {
            if (successAlert && successAlert.parentNode) {
                successAlert.remove();
            }
        }, 3000);
    }
}

// Create global API client instance
window.api = new ApiClient();
