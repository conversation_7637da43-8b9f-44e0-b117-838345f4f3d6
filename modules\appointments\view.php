<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin();

$current_user = $auth->getCurrentUser();
$appointment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$appointment_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

$database = new Database();

// Lấy thông tin lịch hẹn với database mới
$sql = "SELECT a.*,
               b.hoten as patient_name,
               b.sdt as patient_phone,
               b.email as patient_email,
               b.ngaysinh as birth_date,
               b.gioitinh as gender,
               b.di<PERSON> as patient_address,
               u.full_name as created_by_name
        FROM appointments a
        LEFT JOIN benhnhan b ON a.patient_id = b.id
        LEFT JOIN users u ON a.created_by = u.id
        WHERE a.id = ?";

$appointment = $database->fetchOne($sql, [$appointment_id]);

if (!$appointment) {
    header("Location: list.php?error=not_found");
    exit();
}

// <PERSON>ác sĩ tư nhân có thể xem tất cả lịch hẹn

// Xử lý thông báo
$success = '';
$error = '';
if (isset($_GET['success'])) {
    $success = $_GET['success'];
}
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'invalid_status':
            $error = "Trạng thái không hợp lệ!";
            break;
        case 'update_failed':
            $error = "Không thể cập nhật trạng thái!";
            break;
        case 'system_error':
            $error = "Lỗi hệ thống!";
            break;
        default:
            $error = "Có lỗi xảy ra!";
    }
}

function getStatusBadge($status) {
    $badges = [
        'dat_lich' => '<span style="background: #ffc107; color: #000; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600;">📅 Đã đặt</span>',
        'xac_nhan' => '<span style="background: #17a2b8; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600;">✅ Xác nhận</span>',
        'dang_kham' => '<span style="background: #007bff; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600;">🔄 Đang khám</span>',
        'hoan_thanh' => '<span style="background: #28a745; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600;">✅ Hoàn thành</span>',
        'huy_lich' => '<span style="background: #dc3545; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600;">❌ Đã hủy</span>',
        'khong_den' => '<span style="background: #6c757d; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600;">👻 Không đến</span>'
    ];
    return $badges[$status] ?? '<span style="background: #6c757d; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600;">' . $status . '</span>';
}

function getPriorityBadge($priority) {
    $badges = [
        'binh_thuong' => '<span style="background: #e9ecef; color: #495057; padding: 6px 12px; border-radius: 15px; font-size: 12px;">🟢 Bình thường</span>',
        'khan_cap' => '<span style="background: #fff3cd; color: #856404; padding: 6px 12px; border-radius: 15px; font-size: 12px;">🟡 Khẩn cấp</span>',
        'uu_tien' => '<span style="background: #f8d7da; color: #721c24; padding: 6px 12px; border-radius: 15px; font-size: 12px;">🔴 Ưu tiên</span>'
    ];
    return $badges[$priority] ?? '<span style="background: #e9ecef; color: #495057; padding: 6px 12px; border-radius: 15px; font-size: 12px;">' . $priority . '</span>';
}

function getServiceTypeLabel($service_type) {
    $labels = [
        'kham_tong_quat' => '🔍 Khám tổng quát',
        'tay_trang' => '✨ Tẩy trắng',
        'nho_rang' => '🦷 Nhổ răng',
        'han_tram' => '🔧 Hàn trám',
        'dieu_tri_tuy' => '🩺 Điều trị tủy',
        'nieng_rang' => '📐 Niềng răng',
        'cay_ghep' => '🔩 Cấy ghép',
        'phau_thuat' => '⚕️ Phẫu thuật',
        'tu_van' => '💬 Tư vấn'
    ];
    return $labels[$service_type] ?? '🔍 Khám tổng quát';
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chi tiết Lịch hẹn - <?php echo htmlspecialchars($appointment['appointment_code']); ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .appointment-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px auto;
            max-width: 1000px;
        }
        
        .appointment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .appointment-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 4px solid #4CAF50;
        }
        
        .info-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .info-item {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-start;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
            min-width: 140px;
            margin-right: 15px;
        }
        
        .info-value {
            color: #333;
            flex: 1;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        .appointment-timeline {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 4px solid #667eea;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 10px;
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4CAF50;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 15px;
            font-size: 18px;
        }
        
        .actions-section {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #f0f0f0;
        }

        .home-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .home-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
            background: linear-gradient(45deg, #ee5a52, #ff6b6b);
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .appointment-info {
                grid-template-columns: 1fr;
            }
            
            .appointment-header {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="../../dashboard.php" class="home-btn">
        <span style="font-size: 20px;">🏠</span>
        <span>Trang chủ</span>
    </a>
    
    <div class="appointment-container fade-in">
        <!-- Thông báo -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <div class="appointment-header">
            <div>
                <h1>
                    <span style="font-size: 40px;">📅</span>
                    Chi tiết Lịch hẹn
                </h1>
                <p style="color: #666; margin-top: 10px;">
                    Mã lịch hẹn: <strong>LH<?php echo str_pad($appointment['id'], 4, '0', STR_PAD_LEFT); ?></strong>
                </p>
            </div>
            <div style="text-align: right;">
                <?php echo getStatusBadge($appointment['status']); ?>
                <br><br>
                <?php echo getPriorityBadge($appointment['priority']); ?>
            </div>
        </div>
        
        <div class="appointment-info">
            <!-- Thông tin bệnh nhân -->
            <div class="info-section">
                <h3>👤 Thông tin Bệnh nhân</h3>
                <div class="info-item">
                    <span class="info-label">Họ tên:</span>
                    <span class="info-value"><?php echo htmlspecialchars($appointment['patient_name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Số điện thoại:</span>
                    <span class="info-value"><?php echo htmlspecialchars($appointment['patient_phone']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email:</span>
                    <span class="info-value"><?php echo htmlspecialchars($appointment['patient_email'] ?: 'Chưa có'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Ngày sinh:</span>
                    <span class="info-value">
                        <?php 
                        if ($appointment['birth_date']) {
                            $birth_date = new DateTime($appointment['birth_date']);
                            $today = new DateTime();
                            $age = $today->diff($birth_date)->y;
                            echo $birth_date->format('d/m/Y') . " (Tuổi: $age)";
                        } else {
                            echo 'Chưa có';
                        }
                        ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Giới tính:</span>
                    <span class="info-value"><?php echo htmlspecialchars($appointment['gender'] ?: 'Chưa có'); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Địa chỉ:</span>
                    <span class="info-value"><?php echo htmlspecialchars($appointment['patient_address'] ?: 'Chưa có'); ?></span>
                </div>
            </div>
            
            <!-- Thông tin khám -->
            <div class="info-section">
                <h3>👨‍⚕️ Thông tin Khám</h3>
                <div class="info-item">
                    <span class="info-label">Bác sĩ:</span>
                    <span class="info-value">BS. Nguyễn Văn Nam (Bác sĩ tư nhân)</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Chuyên khoa:</span>
                    <span class="info-value">Nha khoa tổng quát</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Dịch vụ:</span>
                    <span class="info-value"><?php echo getServiceTypeLabel($appointment['service_type']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Ngày khám:</span>
                    <span class="info-value">
                        <strong><?php echo date('d/m/Y', strtotime($appointment['appointment_date'])); ?></strong>
                        <?php
                        $date_diff = (strtotime($appointment['appointment_date']) - strtotime(date('Y-m-d'))) / (60*60*24);
                        if ($date_diff == 0) echo ' (Hôm nay)';
                        elseif ($date_diff == 1) echo ' (Ngày mai)';
                        elseif ($date_diff < 0) echo ' (Quá hạn)';
                        ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Giờ khám:</span>
                    <span class="info-value"><strong><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></strong></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Thời gian:</span>
                    <span class="info-value"><?php echo $appointment['duration']; ?> phút</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Chi phí dự kiến:</span>
                    <span class="info-value">
                        <?php
                        if ($appointment['estimated_cost']) {
                            echo number_format($appointment['estimated_cost'], 0, ',', '.') . 'đ';
                        } else {
                            echo 'Chưa xác định';
                        }
                        ?>
                    </span>
                </div>
            </div>
        </div>
        
        <!-- Lý do khám và ghi chú -->
        <?php if ($appointment['reason'] || $appointment['notes']): ?>
        <div class="info-section" style="grid-column: 1 / -1;">
            <h3>📝 Chi tiết</h3>
            <?php if ($appointment['reason']): ?>
            <div class="info-item">
                <span class="info-label">Lý do khám:</span>
                <span class="info-value"><?php echo nl2br(htmlspecialchars($appointment['reason'])); ?></span>
            </div>
            <?php endif; ?>
            
            <?php if ($appointment['notes']): ?>
            <div class="info-item">
                <span class="info-label">Ghi chú:</span>
                <span class="info-value"><?php echo nl2br(htmlspecialchars($appointment['notes'])); ?></span>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <!-- Timeline -->
        <div class="appointment-timeline">
            <h3 style="margin-bottom: 20px; color: #333;">📋 Thông tin tạo</h3>
            <div class="timeline-item">
                <div class="timeline-icon">👤</div>
                <div>
                    <strong>Được tạo bởi:</strong> <?php echo htmlspecialchars($appointment['created_by_name']); ?><br>
                    <small style="color: #666;">
                        <?php echo date('d/m/Y H:i', strtotime($appointment['created_at'])); ?>
                    </small>
                </div>
            </div>
            
            <?php if ($appointment['updated_at'] != $appointment['created_at']): ?>
            <div class="timeline-item">
                <div class="timeline-icon">✏️</div>
                <div>
                    <strong>Cập nhật cuối:</strong><br>
                    <small style="color: #666;">
                        <?php echo date('d/m/Y H:i', strtotime($appointment['updated_at'])); ?>
                    </small>
                </div>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- Actions -->
        <div class="actions-section">
            <a href="edit.php?id=<?php echo $appointment['id']; ?>" class="btn btn-warning">
                ✏️ Chỉnh sửa
            </a>
            
            <?php if ($appointment['status'] != 'hoan_thanh' && $appointment['status'] != 'huy_lich'): ?>
                <?php if ($appointment['status'] == 'dat_lich'): ?>
                    <button onclick="updateStatus('xac_nhan')" class="btn btn-primary">
                        ✅ Xác nhận
                    </button>
                <?php endif; ?>

                <?php if ($appointment['status'] == 'xac_nhan'): ?>
                    <button onclick="updateStatus('dang_kham')" class="btn btn-primary">
                        🔄 Bắt đầu khám
                    </button>
                <?php endif; ?>

                <?php if ($appointment['status'] == 'dang_kham'): ?>
                    <button onclick="updateStatus('hoan_thanh')" class="btn btn-primary">
                        ✅ Hoàn thành
                    </button>
                <?php endif; ?>

                <a href="cancel.php?id=<?php echo $appointment['id']; ?>"
                   class="btn btn-danger"
                   onclick="return confirm('Bạn có chắc muốn hủy lịch hẹn này?')">
                    ❌ Hủy lịch hẹn
                </a>
            <?php endif; ?>
            
            <a href="list.php" class="btn btn-secondary">
                📋 Về danh sách
            </a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.appointment-container').classList.add('fade-in');
        });

        function updateStatus(newStatus) {
            if (confirm('Bạn có chắc muốn cập nhật trạng thái lịch hẹn?')) {
                // Tạo form ẩn để gửi POST request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'update_status.php';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'appointment_id';
                idInput.value = '<?php echo $appointment['id']; ?>';

                const statusInput = document.createElement('input');
                statusInput.type = 'hidden';
                statusInput.name = 'status';
                statusInput.value = newStatus;

                form.appendChild(idInput);
                form.appendChild(statusInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
