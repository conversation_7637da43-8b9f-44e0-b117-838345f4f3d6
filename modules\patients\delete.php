<?php
require_once '../../includes/auth.php';
require_once '../../config/database.php';

$auth = new Auth();
$auth->requireLogin(); // Bác sĩ và trợ lý đều có thể xóa

$current_user = $auth->getCurrentUser();
// Chỉ bác sĩ mới được xóa bệnh nhân
if ($current_user['role'] != 'dentist') {
    header("Location: list.php?error=permission_denied");
    exit();
}

$patient_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$patient_id) {
    header("Location: list.php?error=invalid_id");
    exit();
}

$database = new Database();

// Lấy thông tin bệnh nhân
$sql = "SELECT * FROM benhnhan WHERE id = ?";
$patient = $database->fetchOne($sql, [$patient_id]);

if (!$patient) {
    header("Location: list.php?error=not_found");
    exit();
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] == 'yes') {
        try {
            // Bắt đầu transaction
            $database->beginTransaction();
            
            // Xóa các bản ghi liên quan trước (theo thứ tự foreign key)
            // Xóa hồ sơ khám bệnh
            $database->execute("DELETE FROM medical_records WHERE patient_id = ?", [$patient_id]);

            // Xóa lịch hẹn
            $database->execute("DELETE FROM appointments WHERE patient_id = ?", [$patient_id]);
            
            // Cuối cùng xóa bệnh nhân
            $result = $database->execute("DELETE FROM benhnhan WHERE id = ?", [$patient_id]);
            
            if ($result) {
                $database->commit();
                header("Location: list.php?success=deleted");
                exit();
            } else {
                $database->rollback();
                $error = "Không thể xóa bệnh nhân!";
            }
            
        } catch(Exception $e) {
            $database->rollback();
            $error = "Lỗi hệ thống: " . $e->getMessage();
        }
    } else {
        header("Location: view.php?id=$patient_id");
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Xóa Bệnh nhân - <?php echo htmlspecialchars($patient['hoten']); ?></title>
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .delete-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin: 20px auto;
            max-width: 600px;
            text-align: center;
        }
        
        .delete-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .warning-icon {
            font-size: 80px;
            color: #ff6b6b;
            margin-bottom: 20px;
        }
        
        .patient-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #ff6b6b;
        }
        
        .patient-info h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .warning-text {
            color: #721c24;
            background: #f8d7da;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
            font-weight: 600;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px;">
    <a href="view.php?id=<?php echo $patient_id; ?>" class="btn btn-secondary back-btn">← Về thông tin</a>
    
    <div class="delete-container fade-in">
        <div class="delete-header">
            <div class="warning-icon">⚠️</div>
            <h1 style="color: #ff6b6b;">Xác nhận xóa Bệnh nhân</h1>
        </div>
        
        <?php if (!empty($error)): ?>
            <div class="alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <div class="patient-info">
            <h3>Thông tin bệnh nhân sẽ bị xóa:</h3>
            <div class="info-item">
                <span class="info-label">ID:</span>
                <span class="info-value"><?php echo $patient['id']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Họ tên:</span>
                <span class="info-value"><?php echo htmlspecialchars($patient['hoten']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Số điện thoại:</span>
                <span class="info-value"><?php echo htmlspecialchars($patient['sdt']); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span class="info-value"><?php echo htmlspecialchars($patient['email'] ?: 'Chưa có'); ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">Ngày tạo:</span>
                <span class="info-value"><?php echo date('d/m/Y H:i', strtotime($patient['created_at'])); ?></span>
            </div>
        </div>
        
        <div class="warning-text">
            <strong>⚠️ CẢNH BÁO:</strong><br>
            Hành động này sẽ xóa vĩnh viễn tất cả thông tin của bệnh nhân bao gồm:
            <ul style="text-align: left; margin-top: 10px;">
                <li>Thông tin cá nhân</li>
                <li>Lịch sử khám bệnh</li>
                <li>Hồ sơ bệnh án</li>
                <li>Đơn thuốc</li>
                <li>Lịch hẹn</li>
            </ul>
            <strong>Dữ liệu không thể khôi phục sau khi xóa!</strong>
        </div>
        
        <form method="POST" onsubmit="return confirmDelete()">
            <p style="margin-bottom: 30px; font-size: 18px; color: #333;">
                Bạn có chắc chắn muốn xóa bệnh nhân này không?
            </p>
            
            <button type="submit" name="confirm_delete" value="yes" class="btn btn-danger">
                🗑️ Xác nhận xóa
            </button>
            <a href="view.php?id=<?php echo $patient_id; ?>" class="btn btn-secondary">
                ❌ Hủy bỏ
            </a>
        </form>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('.delete-container').classList.add('fade-in');
        });
        
        function confirmDelete() {
            return confirm('BẠN CÓ CHẮC CHẮN MUỐN XÓA BỆNH NHÂN NÀY?\n\nHành động này không thể hoàn tác!');
        }
    </script>
</body>
</html>
